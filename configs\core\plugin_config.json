{"dashboard": {"enabled": true, "refresh_interval": 300}, "dashboard_core": {"enabled": false, "refresh_interval": 300}, "steam": {"enabled": true, "email_config": {"imap_server": "imap.gmail.com", "imap_port": 993, "use_ssl": true, "credentials": [{"email": "<EMAIL>", "password": "app_password", "username": "steam_user1"}]}, "inventory_config": {"stock_file": "data/steam_stock.json", "auto_update": true, "update_interval": 300}, "order_config": {"auto_ship": true, "chat_messages": {"auth_code_success": "Steam authentication code: {code}", "auth_code_failure": "Failed to retrieve Steam authentication code. Please contact support."}}, "session_config": {"cooldown_time": 600, "request_timeout": 60}}, "netflix": {"enabled": true, "accounts": [{"email": "<EMAIL>", "password": "password", "name": "Netflix Account 1", "enabled": true}], "session_config": {"cooldown_time": 600, "request_timeout": 30, "max_concurrent_requests": 3}, "order_config": {"auto_ship": true, "chat_messages": {"signin_code_success": "Netflix sign-in code: {code} for account: {email}", "signin_code_failure": "Failed to retrieve Netflix sign-in code. Please contact support."}}, "signin_config": {"base_url": "https://www.netflix.com", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36", "retry_attempts": 3}}, "vpn": {"enabled": true, "vpn_api": {"base_url": "https://blueblue.api.limjianhui.com", "username": "admin", "password": "admin123", "timeout": 30}, "server_config": {"servers_file": "configs/services/vpn_servers.json", "connection_timeout": 30}, "client_config": {"default_traffic_gb": 100, "default_expiry_days": 30}, "config_templates": {"templates_file": "configs/services/config_templates.json"}, "redemption": {"enabled": true, "default_expiry_days": 30}}, "ai_chat": {"enabled": false, "ai_config": {"ai_reply_enabled": false, "ai_reply_cooldown_minutes": 60, "ai_system_prompt": "You are a helpful customer service assistant for an online store. Your role is to assist customers with their orders, answer questions about products, and provide support.", "ai_temperature": 1.0, "deepseek_api_key": ""}}, "canva": {"enabled": true, "api_config": {"base_url": "https://api.canva.com", "api_key": "your_canva_api_key", "timeout": 30}, "order_config": {"auto_process": true, "invitation_expiry": 7200, "chat_messages": {"invitation_success": "<PERSON><PERSON> invitation created: {url}", "invitation_failure": "Failed to create Canva invitation. Please contact support."}}, "storage_config": {"orders_file": "configs/data/canva_orders.json", "config_file": "configs/services/canva_config.json"}}, "shopee": {"enabled": true, "api_config": {"timeout": 30, "retry_attempts": 3, "base_url": "https://seller.shopee.com.my"}, "order_config": {"auto_ship": false, "page_size": 20, "default_status_filters": ["to_ship", "shipped", "completed"]}, "chat_config": {"auto_reply": false, "message_templates": {"order_shipped": "Your order has been shipped successfully!", "order_processing": "Your order is being processed."}}}, "chat_commands": {"enabled": true, "webhook_enabled": true, "auto_response_enabled": true, "shopee_integration": {"base_url": "http://shop.api.limjianhui.com", "webhook_endpoint": "/chat-commands/api/webhook", "auto_register": true}, "command_config": {"command_prefix": "#", "case_sensitive": false, "max_response_length": 4000, "max_images_per_response": 5}, "response_config": {"send_typing_indicator": false, "response_delay_ms": 500, "image_send_delay_ms": 1000}}, "vpn_config_generator": {"enabled": true, "api_config": {"enabled": true, "use_vpn_plugin_api": true, "fallback_api_endpoint": "https://blueblue.api.limjianhui.com", "fallback_username": "admin", "fallback_password": "admin123", "timeout": 30}, "generator_settings": {"default_validity_days": 30, "username_prefix": "user", "add_random_suffix": true, "config_format": "vless"}, "command_config": {"command_name": "config", "command_description": "Generate a VPN configuration", "response_text": "🎖️ ᴄᴏɴꜰɪɢ ɪɴꜰᴏʀᴍᴀᴛɪᴏɴ 🎖️", "enabled": true}}, "shopee_auto_boost": {"enabled": true, "boost_interval_hours": 4, "products_per_boost": 5, "auto_start": true, "shopee_api_url": "http://localhost:8000", "product_filters": {"min_stock": 1, "exclude_unlisted": true, "exclude_inactive": true}, "rotation_strategy": "least_recently_boosted", "logging": {"log_boost_attempts": true, "log_product_selection": true}}}