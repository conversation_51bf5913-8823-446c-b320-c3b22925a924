"""
VPN Config Generator Plugin

This plugin handles VPN configuration generation independently from chat commands
to avoid tight coupling between plugins.
"""

import logging
import os
import json
import traceback
from typing import Dict, Any, Optional
from flask import Blueprint

# Import base plugin class
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
from core.plugin_manager import PluginInterface

from .services import VPNConfigGeneratorService, VPNChatCommandService, VPNOrderService
from .routes import create_routes

logger = logging.getLogger(__name__)


class Plugin(PluginInterface):
    """VPN Config Generator Plugin"""
    
    def __init__(self, plugin_manager):
        super().__init__(plugin_manager)
        self.name = "vpn_config_generator"
        self.version = "1.0.0"
        self.description = "Generates VPN configurations using the VPN plugin API or fallback methods"

        # Plugin-specific attributes
        self.config_service: Optional[VPNConfigGeneratorService] = None
        self.chat_command_service: Optional[VPNChatCommandService] = None
        self.order_service: Optional[VPNOrderService] = None
        self.routes_blueprint = None
        self.plugin_dir = os.path.dirname(os.path.abspath(__file__))
        self.url_prefix = "/vpn-config-generator"  # Custom URL prefix
    
    def initialize(self) -> bool:
        """Initialize the plugin"""
        try:
            logger.info(f"Initializing {self.name} plugin...")

            # Initialize services
            self.config_service = VPNConfigGeneratorService(self.plugin_dir, self.config, self.plugin_manager)
            self.chat_command_service = VPNChatCommandService(self.plugin_dir, self.config_service, self.plugin_manager)
            self.order_service = VPNOrderService(self.plugin_dir, self.config)
            
            # Pass config_service to order_service for VPN API access
            self.order_service.config_service = self.config_service

            # Create and register routes
            self.routes_blueprint = create_routes(self)

            # Register API endpoints for external access
            self._register_api_endpoints()

            # Register commands with chat commands plugin
            self._register_chat_commands()

            logger.info(f"{self.name} plugin initialized successfully")
            return True

        except Exception as e:
            logger.error(f"Error initializing {self.name} plugin: {e}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            return False
    
    def _register_api_endpoints(self):
        """Register API endpoints for external plugin access"""
        try:
            # Register generate_config method for external access
            if hasattr(self.plugin_manager, 'register_plugin_method'):
                self.plugin_manager.register_plugin_method(
                    'vpn_config_generator',
                    'generate_config',
                    self.generate_config_external
                )

                self.plugin_manager.register_plugin_method(
                    'vpn_config_generator',
                    'test_connection',
                    self.test_connection_external
                )

                logger.info("Registered external API endpoints")
        except Exception as e:
            logger.warning(f"Could not register external API endpoints: {e}")

    def _register_chat_commands(self):
        """Register VPN commands with the chat commands plugin"""
        try:
            # Get the chat commands plugin
            chat_commands_plugin = self.plugin_manager.get_plugin('chat_commands')
            if not chat_commands_plugin:
                logger.warning("Chat commands plugin not found, cannot register VPN commands")
                return

            # Import the ChatCommand model from chat commands plugin
            from plugins.chat_commands.models import ChatCommand

            # Load commands from centralized config location (managed by chat command service)
            if hasattr(self.chat_command_service, '_commands') and self.chat_command_service._commands:
                commands_data = {k: v.to_dict() for k, v in self.chat_command_service._commands.items()}
            else:
                logger.warning("No commands found in chat command service")
                commands_data = {}

            # Register each command
            for command_key, command_info in commands_data.items():
                command_obj = ChatCommand(
                        command=command_info['command'],
                        description=command_info['description'],
                        response_text=command_info['response_text'],
                        required_params=command_info.get('required_params', []),
                        enabled=command_info.get('enabled', True)
                    )

                # Choose the appropriate handler based on command key (not command name)
                # This allows flexible command names while maintaining handler mapping
                if command_key == 'v':
                    handler = self._handle_config_command
                elif command_key == 'vlist':
                    handler = self._handle_list_command
                elif command_key == 'vuser':
                    handler = self._handle_user_command
                elif command_key == 'vdel':
                    handler = self._handle_delete_command
                elif command_key == 'vrenew':
                    handler = self._handle_renew_command
                elif command_key == 'vtest':
                    handler = self._handle_test_command
                elif command_key == 'vservers':
                    handler = self._handle_servers_command
                elif command_key == 'vhelp':
                    handler = self._handle_help_command
                else:
                    continue

                # Register the command
                success = chat_commands_plugin.register_external_command(
                    command_obj,
                    self.name,
                    handler
                )

                if success:
                    logger.info(f"Successfully registered VPN '{command_info['command']}' command with chat commands plugin")
                else:
                    logger.error(f"Failed to register VPN '{command_info['command']}' command with chat commands plugin")

        except Exception as e:
            logger.error(f"Error registering chat commands: {e}")

    def _handle_config_command(self, message, params):
        """Handle VPN config generation command"""
        try:
            # Import required models
            from plugins.chat_commands.models import CommandResponse
            from .models import VPNConfigRequest

            # Check required parameters
            if len(params) < 4:
                # Get command prefix from chat commands plugin
                chat_commands_plugin = self.plugin_manager.get_plugin('chat_commands')
                prefix = "#"  # default
                if chat_commands_plugin and hasattr(chat_commands_plugin, 'command_service'):
                    prefix = chat_commands_plugin.command_service.get_command_prefix()

                # Get command name from configuration
                command_config = self.chat_command_service.get_command_config()
                error_msg = f"Usage: {prefix}{command_config.command_name} <server|auto> <days> <telco> <plan>\n"
                error_msg += f"• Use 'auto' as server to enable SKU-based server selection\n"
                error_msg += f"• Or specify server ID/name for manual selection"
                return [CommandResponse(text=error_msg)]

            # Extract parameters
            server = params[0]
            days = params[1]
            telco = params[2]
            plan = params[3]

            # Generate username with random suffix
            import random
            username = f"{message.sender_name}-{random.randint(10000, 99999)}"

            # For chat commands, don't use SKU restrictions - allow free server selection
            # This ensures chat commands work without order/product limitations
            sku = None
            var_sku = None

            # Generate VPN config using the config generator service
            config_request = VPNConfigRequest(
                server=server,
                days=days,
                telco=telco,
                plan=plan,
                username=username,
                sku=sku,
                var_sku=var_sku
            )

            result = self.config_service.generate_config(config_request)

            if result.success:
                # Get generated config info to display numeric ID
                generated_configs = self.config_service.get_generated_configs()
                config_info = generated_configs.get(username)
                numeric_id = config_info.get('numeric_id') if config_info else None
                
                # Create response with config information
                response_text = f"🎖️ VPN Config Information 🎖️\n\n"
                response_text += f"📅 Created Date: {result.created_date}\n"
                response_text += f"⏰ Expired Date: {result.expired_date}\n"
                response_text += f"📡 Telco: {telco}\n"
                response_text += f"⏳ Validity: {days} Day(s)\n"
                
                # Add numeric ID if available
                if numeric_id:
                    response_text += f"🆔 Config ID: {numeric_id} (use for #vdel/#vrenew)\n"

                # Add SKU information if available
                if sku or var_sku:
                    response_text += f"🏷️ SKU: {var_sku or sku}\n"
                    if server.lower() == 'auto':
                        response_text += f"🖥️ Server: Auto-selected by SKU\n"
                    else:
                        response_text += f"🖥️ Server: {server} (SKU-enhanced)\n"
                else:
                    response_text += f"🖥️ Server: {server}\n"

                response_text += f"\n🎖️ VPN Config Information 🎖️"

                responses = [CommandResponse(text=response_text)]

                # Add config as a separate message if available
                if result.config:
                    responses.append(CommandResponse(text=result.config))

                return responses
            else:
                error_msg = f"Failed to generate VPN config: {result.error}"
                return [CommandResponse(text=error_msg)]

        except Exception as e:
            logger.error(f"Error handling config command: {e}")
            return [CommandResponse(text="Error processing command. Please try again later.")]

    def _handle_list_command(self, message, params):
        """Handle VPN list command (vlist) - show available servers per telco & plan"""
        try:
            # Import required models
            from plugins.chat_commands.models import CommandResponse

            # Get servers and configurations
            import asyncio
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                data = loop.run_until_complete(self.chat_command_service.get_servers_and_configs())
            finally:
                loop.close()

            if 'error' in data:
                return [CommandResponse(text=f"Error retrieving server information: {data['error']}")]

            # Build response text showing servers available for each telco/plan
            response_text = "📋 Available VPN Configurations by Telco\n\n"

            # Get servers and telcos data
            servers = data.get('servers', [])
            telcos = data.get('telcos', {})
            total_plans = data.get('total_plans', 0)

            # Filter to show only online servers
            online_servers = [s for s in servers if s.get('status') == 'online']
            
            if not online_servers:
                response_text += "❌ No online servers available at the moment.\n\n"
                response_text += "💡 Use #vservers to see all servers and their status."
                return [CommandResponse(text=response_text)]

            if telcos:
                response_text += f"📱 Available Telcos & Plans ({total_plans} total):\n\n"

                for telco_id, telco_info in telcos.items():
                    if not telco_info.get('enabled', True):
                        continue

                    plans = telco_info.get('plans', {})
                    enabled_plans = {name: plan for name, plan in plans.items() if plan.get('enabled', True)}

                    if enabled_plans:
                        response_text += f"📡 {telco_info['name']} ({len(enabled_plans)} plans)\n"
                        
                        # Show plans for this telco
                        response_text += "   📋 Plans: "
                        plan_names = list(enabled_plans.keys())
                        if len(plan_names) <= 4:
                            response_text += ", ".join(plan_names)
                        else:
                            response_text += ", ".join(plan_names[:4]) + f" +{len(plan_names) - 4} more"
                        response_text += "\n\n"
            else:
                response_text += "📱 No telco configurations available\n\n"

            # Server summary
            response_text += f"🖥️ Server Summary: {len(online_servers)} online, {len(servers) - len(online_servers)} offline\n\n"

            # Usage instructions
            response_text += "💡 Usage Examples:\n"
            response_text += f"• #v {online_servers[0]['id']} 30 digi booster\n" if online_servers else "• #v 1 30 digi booster\n"
            response_text += f"• #v {online_servers[0]['name'][:10]} 7 maxis hotlink\n" if online_servers else "• #v SG-01 7 maxis hotlink\n"
            response_text += "• #v auto 30 celcom booster (SKU-based server selection)\n\n"
            response_text += "📋 Use #vservers for detailed server list with status"

            return [CommandResponse(text=response_text)]

        except Exception as e:
            logger.error(f"Error handling list command: {e}")
            return [CommandResponse(text="Error retrieving server and configuration information. Please try again later.")]

    def _handle_user_command(self, message, params):
        """Handle VPN user command (vuser) - show user's VPN configurations"""
        try:
            # Import required models
            from plugins.chat_commands.models import CommandResponse

            # Parse parameters: [username] [page]
            username_filter = None
            page = 1
            
            if len(params) > 0:
                # Check if first param is a username filter (1 or 0)
                if params[0] in ['1', '0']:
                    username_filter = params[0]
                    # Check for page number in second param
                    if len(params) > 1:
                        try:
                            page = int(params[1])
                            if page < 1:
                                page = 1
                        except ValueError:
                            page = 1
                else:
                    # First param is page number
                    try:
                        page = int(params[0])
                        if page < 1:
                            page = 1
                    except ValueError:
                        page = 1

            # Get all generated configs from the service
            all_configs = self.config_service.get_generated_configs()
            
            if not all_configs:
                return [CommandResponse(text="📭 No VPN configurations found.\n\n💡 Use #v command to generate a new configuration.")]

            # Convert to list and apply username filter
            config_list = []
            for username, config_info in all_configs.items():
                config_info['username'] = username
                
                # Apply username filter if specified
                if username_filter is not None:
                    is_active = config_info.get('active', True)
                    if username_filter == '1' and not is_active:
                        continue  # Skip inactive configs when filtering for active (1)
                    elif username_filter == '0' and is_active:
                        continue  # Skip active configs when filtering for inactive (0)
                
                config_list.append(config_info)
            
            # Sort: active configs first, then by created_date (newest first)
            def sort_key(x):
                # Active configs first
                active_priority = not x.get('active', True)
                
                # Sort by created_date (DD-MM-YYYY format) - newest first
                created_date = x.get('created_date', '')
                try:
                    # Parse DD-MM-YYYY format and convert to sortable format
                    if created_date:
                        date_parts = created_date.split('-')
                        if len(date_parts) == 3:
                            # Convert DD-MM-YYYY to YYYY-MM-DD for proper sorting
                            sortable_date = f"{date_parts[2]}-{date_parts[1]}-{date_parts[0]}"
                        else:
                            sortable_date = created_date
                    else:
                        sortable_date = '1900-01-01'  # Very old date for configs without dates
                except:
                    sortable_date = '1900-01-01'
                
                return (active_priority, sortable_date)
            
            config_list.sort(key=sort_key, reverse=True)
            
            # Pagination settings
            items_per_page = 10
            total_pages = (len(config_list) + items_per_page - 1) // items_per_page
            
            # Ensure page is within bounds
            if page > total_pages:
                page = total_pages
            
            # Get items for current page
            start_idx = (page - 1) * items_per_page
            end_idx = start_idx + items_per_page
            page_configs = config_list[start_idx:end_idx]
            
            # Build response with filter indication
            filter_text = ""
            if username_filter == '1':
                filter_text = " (Active Only)"
            elif username_filter == '0':
                filter_text = " (Inactive Only)"
                
            response_text = f"📋 VPN Configurations{filter_text} (Page {page}/{total_pages})\n"
            response_text += f"Total: {len(config_list)} configurations\n\n"
            
            for idx, config in enumerate(page_configs, start=start_idx + 1):
                # Status icon
                status_icon = "✅" if config.get('active', True) else "❌"
                
                # Calculate validity (days remaining)
                validity_text = ""
                try:
                    expired_date = config.get('expired_date', '')
                    if expired_date:
                        # Parse date in DD-MM-YYYY format
                        from datetime import datetime
                        exp_date = datetime.strptime(expired_date, '%d-%m-%Y')
                        now = datetime.now()
                        days_remaining = (exp_date - now).days
                        
                        if days_remaining > 0:
                            validity_text = f" | ⏳ {days_remaining} days left"
                        elif days_remaining == 0:
                            validity_text = f" | ⚠️ Expires today"
                        else:
                            validity_text = f" | ❌ Expired {abs(days_remaining)} days ago"
                except:
                    validity_text = ""
                
                # Format config info without markdown
                response_text += f"{idx}. {status_icon} {config.get('username', 'Unknown')}\n"
                
                # Show numeric ID if available (for vdel/vrenew commands)
                if config.get('numeric_id'):
                    response_text += f"   🆔 ID: {config.get('numeric_id')} (use for #vdel/#vrenew)\n"
                
                response_text += f"   🔑 UUID: {config.get('client_id', 'N/A')}\n"
                response_text += f"   🖥️ Server: {config.get('server_id', 'N/A')}\n"
                response_text += f"   📡 Telco: {config.get('telco', 'N/A')} | 📋 Plan: {config.get('plan', 'N/A')}\n"
                response_text += f"   📅 Created: {config.get('created_date', 'N/A')} | ⏰ Expires: {config.get('expired_date', 'N/A')}{validity_text}\n"
                
                if config.get('sku') or config.get('var_sku'):
                    response_text += f"   🏷️ SKU: {config.get('var_sku') or config.get('sku', 'N/A')}\n"
                    response_text += f"   📦 Order: {config.get('order_sn', 'N/A')}\n"
                
                response_text += "\n"
            
            # Add navigation help
            if total_pages > 1:
                response_text += f"📄 Navigation: Use #vuser {page + 1} for next page" if page < total_pages else ""
                if page > 1:
                    response_text += f" | #vuser {page - 1} for previous page"
                response_text += "\n\n"
            
            # Add management commands help
            response_text += "💡 Management Commands:\n"
            response_text += "• #vuser 1 [page] - Show only active configurations\n"
            response_text += "• #vuser 0 [page] - Show only inactive configurations\n"
            response_text += "• #vdel <id> - Delete a configuration\n"
            response_text += "• #vrenew <id> <days> - Extend expiry date\n"
            response_text += "• #v <server> <days> <telco> <plan> - Create new config"
            
            return [CommandResponse(text=response_text)]

        except Exception as e:
            logger.error(f"Error handling user command: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return [CommandResponse(text="❌ Error retrieving VPN configurations. Please try again later.")]

    def _handle_delete_command(self, message, params):
        """Handle VPN delete command (vdel) - delete a VPN configuration"""
        try:
            # Import required models
            from plugins.chat_commands.models import CommandResponse

            # Check required parameters
            if len(params) < 1:
                return [CommandResponse(text="❌ Usage: #vdel <client_id>\nExample: #vdel 123")]

            try:
                client_id = int(params[0])
            except ValueError:
                return [CommandResponse(text="❌ Invalid client ID. Please provide a numeric client ID.")]

            # Get VPN API service
            vpn_plugin = self.plugin_manager.get_plugin('vpn')
            if not vpn_plugin or not hasattr(vpn_plugin, 'api_service'):
                return [CommandResponse(text="❌ VPN plugin not available. Please check VPN plugin configuration.")]

            vpn_api = vpn_plugin.api_service

            # First, get client info to verify it exists
            client_info = vpn_api.get_client(client_id)
            if not client_info:
                return [CommandResponse(text=f"❌ Client ID {client_id} not found.")]

            # Delete the client
            success = vpn_api.delete_client(client_id)

            if success:
                email = client_info.get('email', 'N/A')
                server_id = client_info.get('server_id', 'N/A')
                server_display = self._get_server_display_name(server_id)

                # Update local storage to mark as inactive
                self.config_service.update_config_status(str(client_id), False)

                response_text = f"🗑️ VPN Configuration Deleted\n\n"
                response_text += f"✅ Successfully deleted client ID: {client_id}\n"
                response_text += f"📧 Email: {email}\n"
                response_text += f"🖥️ Server: {server_display}"

                return [CommandResponse(text=response_text)]
            else:
                return [CommandResponse(text=f"❌ Failed to delete client ID {client_id}. Please try again later.")]

        except Exception as e:
            logger.error(f"Error handling delete command: {e}")
            return [CommandResponse(text="❌ Error deleting VPN configuration. Please try again later.")]

    def _handle_renew_command(self, message, params):
        """Handle VPN renew command (vrenew) - extend a VPN configuration"""
        try:
            # Import required models
            from plugins.chat_commands.models import CommandResponse

            # Check required parameters
            if len(params) < 2:
                return [CommandResponse(text="❌ Usage: #vrenew <client_id> <days>\nExample: #vrenew 123 30")]

            try:
                client_id = int(params[0])
                days = int(params[1])
            except ValueError:
                return [CommandResponse(text="❌ Invalid parameters. Please provide numeric client ID and days.")]

            if days <= 0:
                return [CommandResponse(text="❌ Days must be a positive number.")]

            # Get VPN API service
            vpn_plugin = self.plugin_manager.get_plugin('vpn')
            if not vpn_plugin or not hasattr(vpn_plugin, 'api_service'):
                return [CommandResponse(text="❌ VPN plugin not available. Please check VPN plugin configuration.")]

            vpn_api = vpn_plugin.api_service

            # First, get client info to verify it exists
            client_info = vpn_api.get_client(client_id)
            if not client_info:
                return [CommandResponse(text=f"❌ Client ID {client_id} not found.")]

            # Extend the client expiry
            success = vpn_api.extend_client_expiry(client_id, days)

            if success:
                # Get updated client info
                updated_client = vpn_api.get_client(client_id)

                email = client_info.get('email', 'N/A')
                server_id = client_info.get('server_id', 'N/A')
                server_display = self._get_server_display_name(server_id)
                new_expiry = updated_client.get('expired_date', 'N/A') if updated_client else 'N/A'

                # Format new expiry date
                if new_expiry != 'N/A':
                    try:
                        from datetime import datetime
                        exp_date = datetime.fromisoformat(new_expiry.replace('Z', '+00:00'))
                        new_expiry = exp_date.strftime('%Y-%m-%d %H:%M')
                        # Update local storage with new expiry date
                        new_expiry_for_storage = exp_date.strftime('%d-%m-%Y')
                        self.config_service.update_config_expiry(str(client_id), new_expiry_for_storage)
                    except:
                        pass

                response_text = f"🔄 VPN Configuration Renewed\n\n"
                response_text += f"✅ Successfully extended client ID: {client_id}\n"
                response_text += f"📧 Email: {email}\n"
                response_text += f"🖥️ Server: {server_display}\n"
                response_text += f"📅 Extended by: {days} days\n"
                response_text += f"⏰ New expiry: {new_expiry}"

                return [CommandResponse(text=response_text)]
            else:
                return [CommandResponse(text=f"❌ Failed to renew client ID {client_id}. Please try again later.")]

        except Exception as e:
            logger.error(f"Error handling renew command: {e}")
            return [CommandResponse(text="❌ Error renewing VPN configuration. Please try again later.")]

    def _handle_test_command(self, message, params):
        """Handle VPN test command (vtest) - test API connectivity"""
        try:
            # Import required models
            from plugins.chat_commands.models import CommandResponse

            response_text = "🔧 VPN API Test Results\n\n"

            # Test 1: Plugin availability
            vpn_plugin = self.plugin_manager.get_plugin('vpn')
            if not vpn_plugin:
                response_text += "❌ VPN plugin not found\n"
                return [CommandResponse(text=response_text)]
            else:
                response_text += "✅ VPN plugin found\n"

            # Test 2: API service availability
            if not hasattr(vpn_plugin, 'api_service'):
                response_text += "❌ VPN API service not available\n"
                return [CommandResponse(text=response_text)]
            else:
                response_text += "✅ VPN API service available\n"

            vpn_api = vpn_plugin.api_service

            # Test 3: API configuration
            if hasattr(vpn_api, 'base_url') and vpn_api.base_url:
                response_text += f"✅ API URL configured: {vpn_api.base_url}\n"
            else:
                response_text += "❌ API URL not configured\n"

            # Check username/password configuration
            if hasattr(vpn_api, 'username') and vpn_api.username:
                response_text += f"✅ API username configured: {vpn_api.username}\n"
            else:
                response_text += "❌ API username not configured\n"

            if hasattr(vpn_api, 'password') and vpn_api.password:
                response_text += "✅ API password configured\n"
            else:
                response_text += "❌ API password not configured\n"

            # Check authentication token status
            if hasattr(vpn_api, 'access_token') and vpn_api.access_token:
                response_text += "✅ Authentication token available\n"
                if hasattr(vpn_api, 'token_expires_at') and vpn_api.token_expires_at:
                    from datetime import datetime
                    if vpn_api.token_expires_at > datetime.now():
                        response_text += "✅ Authentication token is valid\n"
                    else:
                        response_text += "⚠️  Authentication token expired\n"
            else:
                response_text += "⚠️  No authentication token (will authenticate on first request)\n"

            # Test 4: Authentication
            try:
                response_text += "\n🔄 Testing authentication...\n"
                auth_success = vpn_api._ensure_authenticated()
                if auth_success:
                    response_text += "✅ Authentication successful\n"
                else:
                    response_text += "❌ Authentication failed\n"
                    return [CommandResponse(text=response_text)]
            except Exception as auth_error:
                response_text += f"❌ Authentication error: {str(auth_error)}\n"
                return [CommandResponse(text=response_text)]

            # Test 5: API connectivity
            try:
                response_text += "\n🔄 Testing API connectivity...\n"
                clients_response = vpn_api.get_clients(limit=1)

                if clients_response is None:
                    response_text += "❌ API returned None\n"
                elif isinstance(clients_response, dict):
                    response_text += f"✅ API responded with dict\n"
                    response_text += f"   Keys: {list(clients_response.keys())}\n"

                    if 'items' in clients_response:
                        items = clients_response['items']
                        response_text += f"   Items count: {len(items)}\n"
                    elif 'data' in clients_response:
                        data = clients_response['data']
                        response_text += f"   Data count: {len(data)}\n"
                    elif 'clients' in clients_response:
                        clients = clients_response['clients']
                        response_text += f"   Clients count: {len(clients)}\n"
                        response_text += f"   Total: {clients_response.get('total', 'N/A')}\n"
                    else:
                        response_text += "   ⚠️  No 'items', 'data', or 'clients' key found\n"

                elif isinstance(clients_response, list):
                    response_text += f"✅ API responded with list\n"
                    response_text += f"   Items count: {len(clients_response)}\n"
                else:
                    response_text += f"⚠️  Unexpected response type: {type(clients_response)}\n"

                response_text += "✅ API connectivity test passed\n"

            except Exception as api_error:
                response_text += f"❌ API connectivity failed: {str(api_error)}\n"

            response_text += "\n💡 If tests fail, check VPN plugin configuration"

            return [CommandResponse(text=response_text)]

        except Exception as e:
            logger.error(f"Error handling test command: {e}")
            return [CommandResponse(text="❌ Error running VPN API test. Please try again later.")]

    def _handle_servers_command(self, message, params):
        """Handle VPN servers command (vservers) - list all servers with IDs"""
        try:
            # Import required models
            from plugins.chat_commands.models import CommandResponse

            # Get VPN API service
            vpn_plugin = self.plugin_manager.get_plugin('vpn')
            if not vpn_plugin or not hasattr(vpn_plugin, 'api_service'):
                return [CommandResponse(text="❌ VPN plugin not available. Please check VPN plugin configuration.")]

            vpn_api = vpn_plugin.api_service

            # Ensure authentication first
            try:
                logger.info(f"Ensuring authentication for VPN API...")
                auth_success = vpn_api._ensure_authenticated()
                if not auth_success:
                    return [CommandResponse(text=f"❌ VPN API authentication failed. Please check username/password configuration.")]

                # Get servers from VPN API
                logger.info(f"Fetching servers from VPN API...")
                servers_response = vpn_api.get_servers()

                if not servers_response:
                    return [CommandResponse(text=f"❌ VPN API is not responding. Please check API configuration and try again.")]

                # Handle different response formats
                servers_data = []
                if isinstance(servers_response, list):
                    servers_data = servers_response
                elif isinstance(servers_response, dict):
                    if 'data' in servers_response:
                        servers_data = servers_response['data']
                    elif 'servers' in servers_response:
                        servers_data = servers_response['servers']
                    elif servers_response.get('success'):
                        servers_data = servers_response.get('data', [])
                    else:
                        # Assume the dict itself contains server data
                        servers_data = [servers_response] if servers_response else []

                if not servers_data:
                    return [CommandResponse(text="📭 No servers found in VPN API response.")]

            except Exception as api_error:
                logger.error(f"VPN API error: {api_error}")
                return [CommandResponse(text=f"❌ VPN API error: {str(api_error)}. Please check API configuration.")]

            # Build response with server list
            response_text = "🖥️ Available VPN Servers\n\n"

            # Sort servers by ID for consistent display
            try:
                servers_data.sort(key=lambda x: x.get('id', 0))
            except:
                pass  # If sorting fails, continue with unsorted list

            active_count = 0
            offline_count = 0

            for server in servers_data:
                server_id = server.get('id', 'N/A')
                server_name = server.get('name', f'Server {server_id}')
                is_active = server.get('is_active', False)
                host = server.get('host', '')
                description = server.get('description', '')
                health_status = server.get('health_status', 'unknown')

                # Status emoji and text
                if is_active:
                    status_emoji = "🟢"
                    status_text = "Online"
                    active_count += 1
                else:
                    status_emoji = "🔴"
                    status_text = "Offline"
                    offline_count += 1

                # Build server info line
                response_text += f"{status_emoji} ID: {server_id} - {server_name}\n"

                # Add additional info if available
                if description and description != server_name:
                    response_text += f"   📍 Location: {description}\n"
                if host:
                    response_text += f"   🌐 Host: {host}\n"
                if health_status != 'unknown':
                    response_text += f"   💓 Health: {health_status}\n"

                response_text += f"   📊 Status: {status_text}\n\n"

            # Summary
            total_count = len(servers_data)
            response_text += f"📊 Summary: {total_count} total servers ({active_count} online, {offline_count} offline)\n\n"

            # Usage instructions
            response_text += "💡 Usage Examples:\n"
            response_text += "• #v 1 30 digi unlimited (using server ID)\n"
            response_text += "• #v SG-01 7 maxis basic (using server name)\n\n"
            response_text += "📋 Use #vlist to see telcos and plans"

            return [CommandResponse(text=response_text)]

        except Exception as e:
            logger.error(f"Error handling servers command: {e}")
            return [CommandResponse(text="❌ Error retrieving server list. Please try again later.")]

    def _handle_help_command(self, message, params):
        """Handle VPN help command (vhelp) - show help for all VPN commands"""
        try:
            # Import required models
            from plugins.chat_commands.models import CommandResponse

            # Get command prefix from chat commands plugin
            chat_commands_plugin = self.plugin_manager.get_plugin('chat_commands')
            prefix = "#"  # default
            if chat_commands_plugin and hasattr(chat_commands_plugin, 'command_service'):
                prefix = chat_commands_plugin.command_service.get_command_prefix()

            # Build help response
            response_text = "📚 VPN Commands Help\n\n"
            response_text += "Available commands for VPN configuration management:\n\n"

            # Config generation command
            response_text += f"🎖️ {prefix}v - Generate VPN Configuration\n"
            response_text += f"   Usage: {prefix}v <server> <days> <telco> <plan>\n"
            response_text += f"   Example: {prefix}v 11 30 digi unlimited\n"
            response_text += f"   Example: {prefix}v SG-01 7 maxis basic\n\n"

            # Server listing commands
            response_text += f"🖥️ {prefix}vservers - List All VPN Servers\n"
            response_text += f"   Usage: {prefix}vservers\n"
            response_text += f"   Shows all available servers with IDs and status\n\n"

            response_text += f"📋 {prefix}vlist - List Servers & Configurations\n"
            response_text += f"   Usage: {prefix}vlist\n"
            response_text += f"   Shows available servers, telcos, and plans\n\n"

            # User management commands
            response_text += f"👤 {prefix}vuser - View User VPN Configurations\n"
            response_text += f"   Usage: {prefix}vuser [1|0] [page]\n"
            response_text += f"   • {prefix}vuser - Show all configurations\n"
            response_text += f"   • {prefix}vuser 1 - Show only active configurations\n"
            response_text += f"   • {prefix}vuser 0 - Show only inactive configurations\n"
            response_text += f"   • {prefix}vuser 2 - Show page 2 (all configs)\n"
            response_text += f"   • {prefix}vuser 1 2 - Show page 2 (active only)\n\n"

            response_text += f"🗑️ {prefix}vdel - Delete VPN Configuration\n"
            response_text += f"   Usage: {prefix}vdel <client_id>\n"
            response_text += f"   Example: {prefix}vdel 12345\n\n"

            response_text += f"🔄 {prefix}vrenew - Renew VPN Configuration\n"
            response_text += f"   Usage: {prefix}vrenew <client_id> <days>\n"
            response_text += f"   Example: {prefix}vrenew 12345 30\n\n"

            # Test and help commands
            response_text += f"🔧 {prefix}vtest - Test VPN API Connectivity\n"
            response_text += f"   Usage: {prefix}vtest\n"
            response_text += f"   Tests connection to VPN API server\n\n"

            response_text += f"📚 {prefix}vhelp - Show This Help\n"
            response_text += f"   Usage: {prefix}vhelp\n"
            response_text += f"   Displays this help message\n\n"

            # Additional tips
            response_text += "💡 Tips:\n"
            response_text += f"• Use {prefix}vlist to see available servers, telcos, and plans\n"
            response_text += f"• Use {prefix}vservers to see server IDs and status\n"
            response_text += f"• Server parameter accepts both IDs (11) and names (SG-01)\n"
            response_text += f"• Use {prefix}vuser to manage your existing configurations\n\n"

            response_text += "For more detailed information, contact your administrator."

            return [CommandResponse(text=response_text)]

        except Exception as e:
            logger.error(f"Error handling help command: {e}")
            return [CommandResponse(text="❌ Error displaying help information. Please try again later.")]

    def _get_server_display_name(self, server_id):
        """Get server display name from server ID"""
        try:
            # Try to get server info from VPN API
            vpn_plugin = self.plugin_manager.get_plugin('vpn')
            if vpn_plugin and hasattr(vpn_plugin, 'api_service'):
                vpn_api = vpn_plugin.api_service
                try:
                    server_info = vpn_api.get_server(server_id)
                    if server_info and 'name' in server_info:
                        return server_info['name']
                except Exception as e:
                    logger.warning(f"Failed to get server info from VPN API: {e}")

            # Fallback to server ID if name not available
            return f"Server {server_id}"

        except Exception as e:
            logger.error(f"Error getting server display name: {e}")
            return f"Server {server_id}"

    def generate_config_external(self, server: str, days: str, telco: str, plan: str, username: str) -> Dict[str, Any]:
        """External API for generating VPN config"""
        try:
            from .models import VPNConfigRequest
            
            request = VPNConfigRequest(
                server=server,
                days=days,
                telco=telco,
                plan=plan,
                username=username
            )
            
            result = self.config_service.generate_config(request)
            return result.to_dict()
            
        except Exception as e:
            logger.error(f"Error in external generate_config: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def test_connection_external(self) -> Dict[str, Any]:
        """External API for testing VPN connection"""
        try:
            return self.config_service.test_connection()
        except Exception as e:
            logger.error(f"Error in external test_connection: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def shutdown(self) -> bool:
        """Shutdown the plugin"""
        try:
            logger.info(f"Shutting down {self.name} plugin...")

            # Unregister commands from chat commands plugin
            self._unregister_chat_commands()

            # Cleanup resources if needed
            self.config_service = None
            self.chat_command_service = None

            logger.info(f"{self.name} plugin shut down successfully")
            return True

        except Exception as e:
            logger.error(f"Error shutting down {self.name} plugin: {e}")
            return False

    def _unregister_chat_commands(self):
        """Unregister VPN commands from the chat commands plugin"""
        try:
            # Get the chat commands plugin
            chat_commands_plugin = self.plugin_manager.get_plugin('chat_commands')
            if chat_commands_plugin:
                # Load commands from commands.json and unregister each
                commands_file = os.path.join(self.plugin_dir, 'commands.json')
                if os.path.exists(commands_file):
                    with open(commands_file, 'r', encoding='utf-8') as f:
                        commands_data = json.load(f)

                    for command_key, command_info in commands_data.items():
                        command_name = command_info['command']
                        success = chat_commands_plugin.unregister_external_command(command_name, self.name)
                        if success:
                            logger.info(f"Successfully unregistered VPN '{command_name}' command from chat commands plugin")
                        else:
                            logger.warning(f"Failed to unregister VPN '{command_name}' command from chat commands plugin")
        except Exception as e:
            logger.error(f"Error unregistering chat commands: {e}")
    
    def get_status(self) -> Dict[str, Any]:
        """Get plugin status"""
        try:
            status = {
                'name': self.name,
                'version': self.version,
                'enabled': self.enabled,
                'initialized': self.config_service is not None,
                'chat_commands_enabled': self.chat_command_service is not None
            }

            if self.config_service:
                # Get template count
                templates = self.config_service.get_all_templates()
                status['templates_count'] = len(templates)

                # Get configuration status
                api_config = self.config_service.get_api_config()
                status['api_config'] = {
                    'enabled': api_config.enabled,
                    'use_vpn_plugin_api': api_config.use_vpn_plugin_api,
                    'has_fallback_api': bool(api_config.fallback_api_endpoint)
                }

            return status

        except Exception as e:
            logger.error(f"Error getting plugin status: {e}")
            return {
                'name': self.name,
                'version': self.version,
                'enabled': False,
                'error': str(e)
            }
    
    def load_config(self, config: Dict[str, Any]):
        """Load plugin configuration"""
        super().load_config(config)
        
        # Update service config if it exists
        if self.config_service:
            self.config_service.load_config()
        if self.chat_command_service:
            self.chat_command_service.load_commands()
            self.chat_command_service.load_webhook_config()
            self.chat_command_service.load_command_config()
    
    def get_blueprint(self):
        """Return Flask blueprint for this plugin's routes"""
        return self.routes_blueprint

    def get_admin_routes(self) -> Dict[str, str]:
        """Return admin interface routes for this plugin"""
        return {
            "VPN Config Dashboard": "/admin/vpn-config-generator/dashboard",
            "Configuration Management": "/admin/vpn-config-generator/configs",
            "Server Binding Management": "/admin/vpn-config-generator/bindings",
            "SKU Restrictions": "/admin/vpn-config-generator/sku-restrictions",
            "Telco Management": "/admin/vpn-config-generator/telco-management",
            "Redemption Links": "/vpn-config-generator/admin/redemption-links",
            "Bulk Redemption Links": "/vpn-config-generator/admin/redemption-links/bulk",
            "Redemption Analytics": "/vpn-config-generator/admin/redemption-links/analytics"
        }

    def get_config_schema(self) -> Dict[str, Any]:
        """Return configuration schema for this plugin"""
        return {
            "type": "object",
            "properties": {
                "enabled": {
                    "type": "boolean",
                    "default": True,
                    "description": "Enable/disable the VPN config generator plugin"
                },
                "api_config": {
                    "type": "object",
                    "properties": {
                        "enabled": {
                            "type": "boolean",
                            "default": True,
                            "description": "Enable VPN config generation"
                        },
                        "use_vpn_plugin_api": {
                            "type": "boolean",
                            "default": True,
                            "description": "Use VPN plugin API (recommended)"
                        },
                        "fallback_api_endpoint": {
                            "type": "string",
                            "default": "",
                            "description": "Fallback API endpoint"
                        },
                        "fallback_username": {
                            "type": "string",
                            "default": "",
                            "description": "Fallback API username"
                        },
                        "fallback_password": {
                            "type": "string",
                            "default": "",
                            "description": "Fallback API password"
                        },
                        "timeout": {
                            "type": "integer",
                            "default": 30,
                            "description": "API timeout in seconds"
                        }
                    }
                },
                "generator_settings": {
                    "type": "object",
                    "properties": {
                        "default_validity_days": {
                            "type": "integer",
                            "default": 30,
                            "description": "Default validity in days"
                        },
                        "username_prefix": {
                            "type": "string",
                            "default": "user",
                            "description": "Username prefix"
                        },
                        "add_random_suffix": {
                            "type": "boolean",
                            "default": True,
                            "description": "Add random suffix to username"
                        },
                        "config_format": {
                            "type": "string",
                            "default": "vless",
                            "description": "Configuration format"
                        }
                    }
                }
            }
        }
