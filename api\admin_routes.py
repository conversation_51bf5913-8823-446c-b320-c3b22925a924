from flask import Blueprint, render_template, request, jsonify, current_app, redirect, url_for, session
from urllib.parse import urlparse
from config import load_config, save_config, update_config, save_sent_orders, sent_orders, ADMIN_CREDENTIALS
from services.session_service import session_manager
import json
import logging
from datetime import datetime, timedelta
from collections import defaultdict

logger = logging.getLogger(__name__)

def _is_safe_url(target, host_url):
    """Check if a URL is safe for redirection."""
    if not target:
        return False

    # Parse the target URL
    parsed_target = urlparse(target)
    parsed_host = urlparse(host_url)

    # Allow relative URLs (no scheme or netloc)
    if not parsed_target.scheme and not parsed_target.netloc:
        return True

    # Allow URLs with the same scheme and netloc as the host
    return (parsed_target.scheme == parsed_host.scheme and
            parsed_target.netloc == parsed_host.netloc)
import os
from flask_cors import cross_origin
from functools import wraps
from services.stock_service import get_all_stock
from services.manual_order_service import get_all_manual_orders
from services.netflix_service import load_netflix_sessions, save_netflix_sessions
from services.netflix_service import load_netflix_sessions, save_netflix_sessions
from services.netflix_session_service import netflix_session_manager
import time

from utils.credential_manager import credential_manager
from services.health_monitoring_service import health_monitoring_service
from utils.fake_order_security import fake_order_security, filter_fake_orders_from_production

MANUAL_ORDERS_FILE = 'configs/data/manual_orders.json'

def load_dashboard_data():
    file_path = 'configs/data/dashboard_data.json'
    default_data = {
        'auth_code_records': [],
        'daily_stats': {},
        'last_update': None
    }

    if os.path.exists(file_path):
        try:
            with open(file_path, 'r') as f:
                data = json.load(f)
                # Ensure all required keys exist
                for key in default_data:
                    if key not in data:
                        data[key] = default_data[key]
                return data
        except (json.JSONDecodeError, Exception) as e:
            print(f"Error loading dashboard data: {e}")
            # If file is corrupted, recreate it with default data
            save_dashboard_data(default_data)
            return default_data

    return default_data

def save_dashboard_data(data):
    with open('configs/data/dashboard_data.json', 'w') as f:
        json.dump(data, f)

admin_bp = Blueprint('admin', __name__)

def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'admin_logged_in' not in session:
            return redirect(url_for('admin.login'))
        return f(*args, **kwargs)
    return decorated_function

@admin_bp.route('/admin/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        data = request.json
        if data['username'] == ADMIN_CREDENTIALS['username'] and data['password'] == ADMIN_CREDENTIALS['password']:
            session['admin_logged_in'] = True
            next_page = request.args.get('next')
            # Check if next_page is safe (relative URL or same host)
            if not next_page or not _is_safe_url(next_page, request.host_url):
                next_page = url_for('admin.dashboard')
            return jsonify({"message": "Login successful", "next_url": next_page}), 200
        else:
            return jsonify({"error": "Invalid credentials"}), 401
    return render_template('admin_login.html')


@admin_bp.route('/admin/logout')
def logout():
    session.pop('admin_logged_in', None)
    return redirect(url_for('admin.login'))

@admin_bp.route('/admin', methods=['GET'])
@admin_bp.route('/admin/dashboard', methods=['GET'])
@login_required
def dashboard():
    config = load_config()
    email_config_count = len(config.get('EMAIL_CONFIGS', {}))
    steam_credential_count = len(config.get('STEAM_CREDENTIALS', {}))

    # Load dashboard data from JSON
    dashboard_data = load_dashboard_data()
    auth_code_records = dashboard_data['auth_code_records']

    # Update auth_code_records with new records from session_manager
    new_records = session_manager.get_auth_code_records()
    if new_records:
        auth_code_records.extend(new_records)
        dashboard_data['auth_code_records'] = auth_code_records
        dashboard_data['last_update'] = datetime.now().isoformat()
        save_dashboard_data(dashboard_data)

    # Calculate statistics
    today = datetime.now().date()
    successful_redeems_today = 0
    account_redeems = defaultdict(int)
    order_redeems = defaultdict(int)

    for record in auth_code_records:
        record_date = datetime.strptime(record['timestamp'], '%Y-%m-%d %H:%M:%S').date()
        if record_date == today and record['status'] == 'Success':
            successful_redeems_today += 1

        account_redeems[record['username']] += 1
        order_redeems[record['order_id']] += 1

    # Enhance summary
    summary = {
        'total_requests': len(auth_code_records),
        'successful_requests': sum(1 for r in auth_code_records if r['status'] == 'Success'),
        'failed_requests': sum(1 for r in auth_code_records if r['status'] == 'Failure'),
        'unique_accounts': len(account_redeems),
        'unique_orders': len(order_redeems),
        'success_rate': round(sum(1 for r in auth_code_records if r['status'] == 'Success') / len(auth_code_records) * 100, 2) if auth_code_records else 0,
    }

    # Calculate daily statistics for the last 7 days
    daily_stats = defaultdict(lambda: {'total': 0, 'success': 0, 'failure': 0})
    end_date = datetime.now().date()
    start_date = end_date - timedelta(days=6)
    date_range = [start_date + timedelta(days=i) for i in range(7)]

    for record in auth_code_records:
        record_date = datetime.strptime(record['timestamp'], '%Y-%m-%d %H:%M:%S').date()
        if start_date <= record_date <= end_date:
            daily_stats[record_date.isoformat()]['total'] += 1
            if record['status'] == 'Success':
                daily_stats[record_date.isoformat()]['success'] += 1
            elif record['status'] == 'Failure':
                daily_stats[record_date.isoformat()]['failure'] += 1

    # Update daily_stats in dashboard_data
    dashboard_data['daily_stats'] = daily_stats
    save_dashboard_data(dashboard_data)

    # Prepare data for charts
    chart_data = {
        'dates': [date.strftime('%Y-%m-%d') for date in date_range],
        'total_requests': [daily_stats[date.isoformat()]['total'] for date in date_range],
        'successful_requests': [daily_stats[date.isoformat()]['success'] for date in date_range],
        'failed_requests': [daily_stats[date.isoformat()]['failure'] for date in date_range],
    }

    # Get health status for external APIs
    health_summary = {
        'overall_status': 'loading',
        'total_services': 0,
        'healthy_services': 0,
        'degraded_services': 0,
        'unhealthy_services': 0,
        'services': {},
        'last_updated': datetime.now().isoformat()
    }

    return render_template('dashboard.html',
                           email_config_count=email_config_count,
                           steam_credential_count=steam_credential_count,
                           auth_code_records=auth_code_records,
                           successful_redeems_today=successful_redeems_today,
                           account_redeems=dict(account_redeems),
                           order_redeems=dict(order_redeems),
                           summary=summary,
                           chart_data=json.dumps(chart_data),
                           health_status=health_summary)


@admin_bp.route('/admin/system_config', methods=['GET'])
@login_required
def system_config():
    return render_template('system_config.html')

@admin_bp.route('/admin/self_redeem_skus', methods=['GET'])
@login_required
def self_redeem_skus():
    return render_template('self_redeem_skus.html')

@admin_bp.route('/steam/settings')
@login_required
def steam_settings():
    return render_template('steam_settings.html')

@admin_bp.route('/auto-chat')
@login_required
def auto_chat():
    return render_template('auto_chat.html')

@admin_bp.route('/stock')
@login_required
def stock():
    return render_template('stock.html')

@admin_bp.route('/netflix/accounts')
@login_required
def netflix_accounts():
    return render_template('netflix_accounts.html')

@admin_bp.route('/netflix/settings')
@login_required
def netflix_settings():
    return render_template('netflix_settings.html')

@admin_bp.route('/admin/credentials', methods=['GET'])
@login_required
def credentials():
    return render_template('credentials.html')

@admin_bp.route('/admin/get_config', methods=['GET'])
@login_required
def get_config():
    try:
        config = load_config()

        # Get current credentials from credential manager
        credentials = credential_manager.get_credentials()

        # Update config with current credentials before sending
        config['AUTHORIZATION_CODE'] = credentials['authorization_code']
        config['COOKIE'] = credentials['cookie']

        return jsonify(config)
    except Exception as e:
        print(f"Error in get_config: {str(e)}")
        return jsonify({"error": str(e)}), 500

@admin_bp.route('/admin/update_config', methods=['POST'])
@login_required
def update_config_route():
    try:
        new_config = request.json

        # Update credentials if they are included in the new config
        if 'AUTHORIZATION_CODE' in new_config or 'COOKIE' in new_config:
            credential_manager.update_credentials(
                authorization_code=new_config.get('AUTHORIZATION_CODE'),
                cookie=new_config.get('COOKIE')
            )

        save_config(new_config)
        update_config()
        return jsonify({"message": "Configuration updated successfully"}), 200
    except Exception as e:
        return jsonify({"error": f"Failed to update configuration: {str(e)}"}), 500

@admin_bp.route('/admin/add_email_config', methods=['POST'])
@login_required
def add_email_config():
    data = request.json
    config = load_config()
    config['EMAIL_CONFIGS'][data['key']] = {
        "email": data['email'],
        "app_password": data['app_password']
    }
    save_config(config)
    update_config()
    return jsonify({"message": "Email configuration added successfully"}), 200

@admin_bp.route('/admin/add_steam_credential', methods=['POST'])
@login_required
def add_steam_credential():
    data = request.json
    config = load_config()
    config['STEAM_CREDENTIALS'][data['key']] = {
        "password": data['password']
    }
    save_config(config)
    update_config()
    return jsonify({"message": "Steam credential added successfully"}), 200

@admin_bp.route('/admin/delete_email_config', methods=['POST'])
@login_required
def delete_email_config():
    data = request.json
    config = load_config()
    if data['key'] in config['EMAIL_CONFIGS']:
        del config['EMAIL_CONFIGS'][data['key']]
        save_config(config)
        update_config()
        return jsonify({"message": "Email configuration deleted successfully"}), 200
    else:
        return jsonify({"message": "Email configuration not found"}), 404

@admin_bp.route('/admin/delete_steam_credential', methods=['POST'])
@login_required
def delete_steam_credential():
    data = request.json
    config = load_config()
    if data['key'] in config['STEAM_CREDENTIALS']:
        del config['STEAM_CREDENTIALS'][data['key']]
        save_config(config)
        update_config()
        return jsonify({"message": "Steam credential deleted successfully"}), 200
    else:
        return jsonify({"message": "Steam credential not found"}), 404

@admin_bp.route('/admin/update_email_config', methods=['POST'])
@login_required
def update_email_config():
    data = request.json
    config = load_config()
    if data['key'] in config['EMAIL_CONFIGS']:
        config['EMAIL_CONFIGS'][data['key']] = {
            "email": data['email'],
            "app_password": data['app_password']
        }
        save_config(config)
        update_config()
        return jsonify({"message": "Email configuration updated successfully"}), 200
    else:
        return jsonify({"message": "Email configuration not found"}), 404

@admin_bp.route('/admin/update_steam_credential', methods=['POST'])
@login_required
def update_steam_credential():
    data = request.json
    config = load_config()
    if data['key'] in config['STEAM_CREDENTIALS']:
        config['STEAM_CREDENTIALS'][data['key']] = {
            "password": data['password']
        }
        save_config(config)
        update_config()
        return jsonify({"message": "Steam credential updated successfully"}), 200
    else:
        return jsonify({"message": "Steam credential not found"}), 404

@admin_bp.route('/admin/reset_auth_code_records', methods=['POST'])
@login_required
def reset_auth_code_records():
    session_manager.reset_auth_code_records()
    # Reset the stored dashboard data
    dashboard_data = {'auth_code_records': [], 'daily_stats': {}, 'last_update': datetime.now().isoformat()}
    save_dashboard_data(dashboard_data)
    return jsonify({"message": "Authentication code request records reset successfully"}), 200

@admin_bp.route('/admin/update_shopee_credentials', methods=['POST'])
@cross_origin()
def update_shopee_credentials():
    try:
        data = request.json
        config = load_config()

        # Get bearer token and cookies
        bearer_token = data.get('bearerToken', '')
        cookies = data.get('cookies', '')

        # Update config file
        config['AUTHORIZATION_CODE'] = bearer_token

        # Handle different cookie formats
        if isinstance(cookies, (dict, list)):
            # Store as JSON object
            config['COOKIE_JSON'] = cookies
            # Also store as string for backward compatibility
            if isinstance(cookies, list):
                # Convert array format to string
                cookie_str = '; '.join([f"{c.get('name', '')}={c.get('value', '')}"
                                      for c in cookies if 'name' in c and 'value' in c])
            else:
                # Convert object format to string
                cookie_str = '; '.join([f"{name}={value}" for name, value in cookies.items()])
            config['COOKIE'] = cookie_str
        else:
            # Store as string
            config['COOKIE'] = cookies

        save_config(config)
        update_config()

        # Update credential manager directly
        credential_manager.update_credentials(
            authorization_code=bearer_token,
            cookie=cookies
        )

        # Log the update
        current_app.logger.info("Credentials updated successfully")

        return jsonify({"message": "Credentials updated successfully"}), 200
    except Exception as e:
        current_app.logger.error(f"Error updating credentials: {str(e)}")
        return jsonify({"error": f"Failed to update credentials: {str(e)}"}), 500


@admin_bp.route('/admin/get_cooldowns', methods=['GET'])
@login_required
def get_cooldowns():
    """
    Retrieves all active cooldowns.
    """
    try:
        cooldowns = session_manager.get_all_cooldowns()
        return jsonify({"cooldowns": cooldowns}), 200
    except Exception as e:
        print(f"Error getting cooldowns: {e}")
        return jsonify({"error": "Failed to retrieve cooldowns"}), 500

@admin_bp.route('/admin/reset_cooldown', methods=['POST'])
@login_required
def reset_cooldown():
    """
    Resets the cooldown for a specific user and order_id.
    Expects JSON payload with 'username' and 'order_id'.
    """
    data = request.json
    username = data.get('username')
    order_id = data.get('order_id')

    if not username or not order_id:
        return jsonify({"error": "Username and order_id are required"}), 400

    success = session_manager.reset_cooldown(username, order_id)
    if success:
        return jsonify({"message": f"Cooldown reset for {username}:{order_id}"}), 200
    else:
        return jsonify({"error": f"Session not found for {username}:{order_id}"}), 404

@admin_bp.route('/admin/update_auto_redeem', methods=['POST'])
@login_required
def update_auto_redeem():
    new_config = request.json
    save_config(new_config)
    update_config()
    return jsonify({"message": "Auto Redeem configuration updated successfully"}), 200

@admin_bp.route('/admin/get_sent_orders', methods=['GET'])
@login_required
def get_sent_orders_route():
    return jsonify({"sent_orders": list(sent_orders)}), 200

@admin_bp.route('/admin/reset_sent_orders', methods=['POST'])
@login_required
def reset_sent_orders():
    sent_orders.clear()
    save_sent_orders(sent_orders)
    return jsonify({"message": "Sent orders reset successfully"}), 200

@admin_bp.route('/admin/delete_sent_order', methods=['POST'])
@login_required
def delete_sent_order():
    data = request.json
    order_sn = data.get('order_sn')
    if order_sn in sent_orders:
        sent_orders.remove(order_sn)
        save_sent_orders(sent_orders)
        return jsonify({"success": True, "message": "Order deleted successfully"}), 200
    else:
        return jsonify({"success": False, "message": "Order not found"}), 404

@admin_bp.route('/admin/sent_orders', methods=['GET'])
@login_required
def sent_orders_page():
    return render_template('sent_orders.html')

@admin_bp.route('/admin/update_manual_orders', methods=['POST'])
@login_required
def update_manual_orders():
    manual_orders = request.json
    with open(MANUAL_ORDERS_FILE, 'w') as f:
        json.dump(manual_orders, f, indent=2)
    return jsonify({"message": "Manual orders updated successfully"})

@admin_bp.route('/admin/get_manual_orders', methods=['GET'])
@login_required
def get_manual_orders():
    try:
        with open(MANUAL_ORDERS_FILE, 'r') as f:
            manual_orders = json.load(f)
    except FileNotFoundError:
        manual_orders = []
    return jsonify(manual_orders)

@admin_bp.route('/admin/inventory', methods=['GET'])
@login_required
def inventory():
    return render_template('inventory.html')

@admin_bp.route('/admin/get_inventory_data', methods=['GET'])
@login_required
def get_inventory_data():
    # Get redeemed items
    with open('configs/data/redeemed_stock.json', 'r') as f:
        redeemed_items = json.load(f)
    redeemed_items_list = [
        {
            'order_sn': order_sn,
            'var_sku': data['var_sku'],
            'item': data['item'],
            'replace_count': data.get('replace_count', 0)
        }
        for order_sn, data in redeemed_items.items()
    ]

    # Get current stock
    current_stock = get_all_stock()

    # Get manual orders
    manual_orders = get_all_manual_orders()

    return jsonify({
        'redeemed_items': redeemed_items_list,
        'current_stock': current_stock,
        'manual_orders': manual_orders
    })

@admin_bp.route('/admin/get_netflix_cooldowns', methods=['GET'])
@login_required
def get_netflix_cooldowns_route():
    try:
        sessions = load_netflix_sessions()
        cooldowns = []
        current_time = time.time()

        for order_sn, order_data in sessions.get('orders', {}).items():
            cooldown_until = order_data.get('cooldown_until')

            # 只显示当前正在冷却中的订单
            if cooldown_until and current_time < cooldown_until:
                remaining = int(cooldown_until - current_time)
                hours = remaining // 3600
                minutes = (remaining % 3600) // 60

                cooldowns.append({
                    'order_sn': order_sn,
                    'account': order_data.get('account'),
                    'last_redeem': order_data.get('last_redeem'),
                    'cooldown_until': cooldown_until,
                    'remaining_time': f"{hours}h {minutes}m",
                    'remaining_seconds': remaining
                })

        # 按剩余冷却时间排序
        cooldowns.sort(key=lambda x: x['remaining_seconds'])
        return jsonify({"cooldowns": cooldowns}), 200
    except Exception as e:
        current_app.logger.error(f"Error getting Netflix cooldowns: {str(e)}")
        return jsonify({"error": "Failed to retrieve cooldowns"}), 500

@admin_bp.route('/admin/api/health/summary')
@login_required
def get_health_summary():
    """Get overall health summary for all monitored services"""
    try:
        summary = health_monitoring_service.get_overall_health_summary()
        return jsonify({
            'success': True,
            'data': summary
        })
    except Exception as e:
        print(f"Error getting health summary: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@admin_bp.route('/admin/api/core/stats')
@login_required
def get_core_stats():
    """Get core system statistics for dashboard widgets"""
    try:
        config = load_config()
        email_config_count = len(config.get('EMAIL_CONFIGS', {}))
        steam_credential_count = len(config.get('STEAM_CREDENTIALS', {}))

        # Load dashboard data to get successful redeems today
        dashboard_data = load_dashboard_data()
        auth_code_records = dashboard_data['auth_code_records']

        # Update auth_code_records with new records from session_manager
        new_records = session_manager.get_auth_code_records()
        if new_records:
            auth_code_records.extend(new_records)

        # Calculate successful redeems today
        today = datetime.now().date()
        successful_redeems_today = 0
        for record in auth_code_records:
            record_date = datetime.strptime(record['timestamp'], '%Y-%m-%d %H:%M:%S').date()
            if record_date == today and record['status'] == 'Success':
                successful_redeems_today += 1

        return jsonify({
            'success': True,
            'data': {
                'email_config_count': email_config_count,
                'steam_credential_count': steam_credential_count,
                'successful_redeems_today': successful_redeems_today
            }
        })
    except Exception as e:
        print(f"Error getting core stats: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@admin_bp.route('/admin/api/redeem/stats')
@login_required
def get_redeem_stats():
    """Get redeem statistics for dashboard widgets"""
    try:
        # Load dashboard data
        dashboard_data = load_dashboard_data()
        auth_code_records = dashboard_data['auth_code_records']

        # Update auth_code_records with new records from session_manager
        new_records = session_manager.get_auth_code_records()
        if new_records:
            auth_code_records.extend(new_records)

        # Calculate account and order redeem statistics
        account_redeems = defaultdict(int)
        order_redeems = defaultdict(int)

        for record in auth_code_records:
            account_redeems[record['username']] += 1
            order_redeems[record['order_id']] += 1

        return jsonify({
            'success': True,
            'data': {
                'account_redeems': dict(account_redeems),
                'order_redeems': dict(order_redeems)
            }
        })
    except Exception as e:
        print(f"Error getting redeem stats: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@admin_bp.route('/admin/api/health/service/<service_key>')
@login_required
def get_service_health(service_key):
    """Get detailed health status for a specific service"""
    try:
        service_details = health_monitoring_service.get_service_details(service_key)

        if 'error' in service_details:
            return jsonify({
                'success': False,
                'error': service_details['error']
            }), 404

        return jsonify({
            'success': True,
            'data': service_details
        })
    except Exception as e:
        print(f"Error getting service health for {service_key}: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@admin_bp.route('/admin/api/health/refresh', methods=['POST'])
@login_required
def refresh_health_status():
    """Refresh health status for all monitored services"""
    try:
        # Clear the cache to force fresh health checks
        health_monitoring_service.clear_cache()

        # Get fresh health summary
        summary = health_monitoring_service.get_overall_health_summary()

        return jsonify({
            'success': True,
            'message': 'Health status refreshed successfully',
            'data': summary
        })
    except Exception as e:
        print(f"Error refreshing health status: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500





@admin_bp.route('/admin/reset_netflix_cooldown', methods=['POST'])
@login_required
def reset_netflix_cooldown_route():
    try:
        data = request.json
        order_sn = data.get('order_sn')

        if not order_sn:
            return jsonify({"error": "order_sn is required"}), 400

        sessions = load_netflix_sessions()
        if 'orders' in sessions and order_sn in sessions['orders']:
            # Reset cooldown in the file
            sessions['orders'][order_sn]['cooldown_until'] = None
            sessions['orders'][order_sn]['last_redeem'] = None
            save_netflix_sessions(sessions)

            # Also reset in the session manager for consistency
            netflix_session_manager.sessions[order_sn] = {'cooldown_until': None}
            netflix_session_manager.sessions[order_sn] = {'last_redeem': None}

            current_app.logger.info(f"Netflix cooldown reset for order {order_sn}")
            return jsonify({"message": f"Netflix cooldown reset for order {order_sn}"}), 200
        else:
            current_app.logger.warning(f"Netflix session not found for order {order_sn}")
            return jsonify({"error": f"Netflix session not found for order {order_sn}"}), 404

    except Exception as e:
        current_app.logger.error(f"Error resetting Netflix cooldown: {str(e)}")
        return jsonify({"error": "Failed to reset cooldown"}), 500

@admin_bp.route('/admin/update_netflix_emails', methods=['POST'])
@login_required
def update_netflix_emails():
    email_updates = request.json

    if not email_updates:
        current_app.logger.warning("No email updates provided")
        return jsonify({'success': False, 'message': 'No email updates provided'}), 400

    try:
        current_app.logger.info(f"Received email updates: {email_updates}")

        # Load Netflix sessions
        sessions = load_netflix_sessions()
        current_app.logger.info(f"Loaded sessions: {sessions}")

        # Update emails
        updates_made = False
        for old_email, new_email in email_updates.items():
            for key, value in sessions.items():
                if value.get('account') == old_email:
                    value['account'] = new_email
                    updates_made = True
                    current_app.logger.info(f"Updated email for session {key}: {old_email} -> {new_email}")

        if updates_made:
            # Save updated sessions
            save_netflix_sessions(sessions)
            current_app.logger.info("Netflix sessions updated and saved successfully")
            return jsonify({'success': True, 'message': 'Netflix emails updated successfully'})
        else:
            current_app.logger.warning("No matching emails found to update")
            return jsonify({'success': False, 'message': 'No matching emails found to update'}), 404

    except Exception as e:
        current_app.logger.error(f"Error updating Netflix emails: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': f'Error updating Netflix emails: {str(e)}'}), 500

@admin_bp.route('/admin/auto_reply')
@login_required
def auto_reply():
    return render_template('auto_reply.html')

@admin_bp.route('/admin/audit-logs')
@login_required
def audit_logs():
    """View audit logs with filtering support"""
    filter_type = request.args.get('filter', 'all')
    
    try:
        # Read security audit logs
        log_file = 'logs/security_audit.log'
        logs = []
        
        if os.path.exists(log_file):
            with open(log_file, 'r', encoding='utf-8') as f:
                for line in f:
                    if line.strip():
                        try:
                            # Parse log line
                            parts = line.strip().split(' - SECURITY - ', 1)
                            if len(parts) == 2:
                                timestamp_part = parts[0]
                                log_data_part = parts[1].split(' - ', 1)
                                
                                if len(log_data_part) == 2:
                                    level = log_data_part[0]
                                    message = log_data_part[1]
                                    
                                    # Try to parse JSON message
                                    try:
                                        log_data = json.loads(message)
                                        
                                        # Filter fake order logs if requested
                                        if filter_type == 'fake_order':
                                            if (log_data.get('event_type', '').startswith('fake_order') or
                                                'fake_order' in message.lower()):
                                                logs.append({
                                                    'timestamp': timestamp_part,
                                                    'level': level,
                                                    'event_type': log_data.get('event_type', 'unknown'),
                                                    'details': log_data.get('details', {}),
                                                    'client_info': log_data.get('client_info', {}),
                                                    'raw_message': message
                                                })
                                        elif filter_type == 'all':
                                            logs.append({
                                                'timestamp': timestamp_part,
                                                'level': level,
                                                'event_type': log_data.get('event_type', 'unknown'),
                                                'details': log_data.get('details', {}),
                                                'client_info': log_data.get('client_info', {}),
                                                'raw_message': message
                                            })
                                    except json.JSONDecodeError:
                                        # Handle non-JSON log entries
                                        if filter_type == 'all' or (filter_type == 'fake_order' and 'fake_order' in message.lower()):
                                            logs.append({
                                                'timestamp': timestamp_part,
                                                'level': level,
                                                'event_type': 'text_log',
                                                'details': {'message': message},
                                                'client_info': {},
                                                'raw_message': message
                                            })
                        except Exception as e:
                            # Skip malformed log lines
                            continue
        
        # Sort logs by timestamp (newest first)
        logs.sort(key=lambda x: x['timestamp'], reverse=True)
        
        # Limit to last 1000 entries for performance
        logs = logs[:1000]
        
        return render_template('audit_logs.html', 
                             logs=logs, 
                             filter_type=filter_type,
                             total_logs=len(logs))
        
    except Exception as e:
        logger.error(f"Error reading audit logs: {e}")
        return render_template('audit_logs.html', 
                             logs=[], 
                             filter_type=filter_type,
                             error=f"Error reading logs: {str(e)}")

@admin_bp.route('/admin/fake-order-security')
@login_required
def fake_order_security_dashboard():
    """Fake order security dashboard"""
    try:
        # Get fake order security statistics
        security_stats = fake_order_security.get_fake_order_statistics()
        
        # Get recent fake order operations from audit logs
        recent_operations = []
        log_file = 'logs/security_audit.log'
        
        if os.path.exists(log_file):
            with open(log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                # Get last 50 lines and filter for fake order operations
                for line in reversed(lines[-50:]):
                    if 'fake_order' in line.lower():
                        try:
                            parts = line.strip().split(' - SECURITY - ', 1)
                            if len(parts) == 2:
                                timestamp_part = parts[0]
                                message_part = parts[1].split(' - ', 1)[1]
                                log_data = json.loads(message_part)
                                
                                recent_operations.append({
                                    'timestamp': timestamp_part,
                                    'event_type': log_data.get('event_type', 'unknown'),
                                    'order_sn': log_data.get('details', {}).get('order_sn', 'unknown'),
                                    'user_id': log_data.get('client_info', {}).get('user_id', 'anonymous'),
                                    'operation': log_data.get('details', {}).get('operation', 'unknown')
                                })
                        except:
                            continue
        
        return render_template('fake_order_security.html',
                             security_stats=security_stats,
                             recent_operations=recent_operations[:20])  # Show last 20 operations
        
    except Exception as e:
        logger.error(f"Error loading fake order security dashboard: {e}")
        return render_template('fake_order_security.html',
                             security_stats={},
                             recent_operations=[],
                             error=f"Error loading dashboard: {str(e)}")

@admin_bp.route('/admin/replace_content', methods=['POST'])
@login_required
def replace_content():
    try:
        # Get data from request
        data = request.json
        replace_type = data['type']
        order_sn = data['order_sn']
        var_sku = data['var_sku']
        item = data['item']
        new_content = data['new_content']
        selected_orders = data.get('selectedOrders', [])  # New: Get list of selected order SNs

        # Load current redeemed stock
        with open('configs/data/redeemed_stock.json', 'r') as f:
            redeemed_stock = json.load(f)

        def process_order_replacement(order_sn, content):
            """Process replacements for a single order"""
            from services.order_service import get_order_details
            order_details, status = get_order_details(order_sn)

            if status == 200 and isinstance(order_details, dict):
                order_data = order_details['data']
                buyer_user = order_data['buyer_user']
                order_item = order_data['order_items'][0]

                # Variables that can be replaced in the content
                variables = {
                    "{order_sn}": order_sn,
                    "{buyer_username}": buyer_user['user_name'],
                    "{item_name}": order_item['product']['name'],
                    "{item_price}": str(order_data['total_price']),
                    "{buyer_name}": order_data['buyer_address_name'],
                    "{buyer_phone}": order_data['buyer_address_phone'],
                    "{create_time}": order_data['create_time'],
                    "{shipping_address}": order_data['shipping_address'],
                    "{item_sku}": order_item['item_model']['sku'],
                    "{item_quantity}": str(order_item['amount']),
                    "{payment_method}": str(order_data['payment_method']),
                    "{shop_name}": order_data['seller_address']['name'],
                    "{escrow_release_time}": order_data['escrow_release_time'],
                    "{buyer_rating}": str(buyer_user['rating_star']),
                    "{order_status}": str(order_data['status']),
                }

                # Process each section of the content
                processed_content = {
                    'header': content['header'],
                    'content': content['content'],
                    'footer': content['footer']
                }

                # Replace variables in each section
                for section in processed_content:
                    if processed_content[section]:
                        text = processed_content[section]
                        for key, value in variables.items():
                            text = text.replace(key, str(value))
                        processed_content[section] = text

                return processed_content
            return content

        # Handle bulk replacement if selected_orders is provided
        if selected_orders:
            for selected_order_sn in selected_orders:
                if selected_order_sn in redeemed_stock:
                    # Increment or initialize replace_count
                    redeemed_stock[selected_order_sn]['replace_count'] = redeemed_stock[selected_order_sn].get('replace_count', 0) + 1
                    processed_content = process_order_replacement(selected_order_sn, new_content)
                    redeemed_stock[selected_order_sn]['item'] = processed_content['content'].strip()
                    send_formatted_message(selected_order_sn, processed_content)
        else:
            if replace_type == 'single':
                if order_sn in redeemed_stock:
                    redeemed_stock[order_sn]['replace_count'] = redeemed_stock[order_sn].get('replace_count', 0) + 1
                    processed_content = process_order_replacement(order_sn, new_content)
                    redeemed_stock[order_sn]['item'] = processed_content['content'].strip()
                    send_formatted_message(order_sn, processed_content)

            elif replace_type == 'var_sku':
                for key, value in redeemed_stock.items():
                    if value['var_sku'] == var_sku:
                        value['replace_count'] = value.get('replace_count', 0) + 1
                        processed_content = process_order_replacement(key, new_content)
                        value['item'] = processed_content['content'].strip()
                        send_formatted_message(key, processed_content)

            elif replace_type == 'content':
                for key, value in redeemed_stock.items():
                    if value['item'] == item:
                        value['replace_count'] = value.get('replace_count', 0) + 1
                        processed_content = process_order_replacement(key, new_content)
                        value['item'] = processed_content['content'].strip()
                        send_formatted_message(key, processed_content)

        # Save the updated content back to file
        with open('configs/data/redeemed_stock.json', 'w') as f:
            json.dump(redeemed_stock, f, indent=2)

        return jsonify({'success': True, 'message': 'Content replaced successfully'})

    except Exception as e:
        print(f"Error in replace_content: {str(e)}")
        return jsonify({'success': False, 'message': str(e)})

def send_formatted_message(order_sn, content_data):
    """Helper function to send formatted chat message"""
    try:
        # Format the complete message with header, content, and footer
        formatted_parts = []

        if content_data.get('header'):
            header_lines = content_data['header'].strip().split('\n')
            formatted_parts.append('\n'.join(header_lines))

        if content_data.get('content'):
            content_lines = content_data['content'].strip().split('\n')
            formatted_parts.append('\n'.join(content_lines))

        if content_data.get('footer'):
            footer_lines = content_data['footer'].strip().split('\n')
            formatted_parts.append('\n'.join(footer_lines))

        # Join all parts with double line breaks
        complete_message = '\n\n'.join(formatted_parts)

        # Send the chat message
        from services.chat_service import send_chat_message, send_image_message
        if complete_message:
            chat_payload = {
                'order_sn': order_sn,
                'text': complete_message,
                'force_send_cancel_order_warning': False,
                'comply_cancel_order_warning': False
            }
            send_chat_message(chat_payload)

        # Send image messages if URLs are provided
        if content_data.get('image_urls'):
            from services.order_service import get_order_details
            order_details, status = get_order_details(order_sn)
            if status == 200 and isinstance(order_details, dict):
                buyer_username = order_details.get('data', {}).get('buyer_user', {}).get('user_name')
                if buyer_username:
                    for image_url in content_data['image_urls']:
                        if image_url:  # Ensure URL is not empty
                            image_payload = {
                                'username': buyer_username,
                                'image_url': image_url,
                                'auto_detect_dimensions': True
                            }
                            print(f"Sending image with payload: {image_payload}")
                            send_image_message(image_payload)
                else:
                    print(f"Could not find buyer_username for order {order_sn}")

    except Exception as e:
        print(f"Error sending formatted message for order {order_sn}: {str(e)}")

@admin_bp.route('/admin/manual_invoice', methods=['GET'])
@login_required
def manual_invoice():
    return render_template('manual_invoice.html')

@admin_bp.route('/canva/manage')
@login_required
def canva_manage():
    try:
        with open('configs/services/canva_config.json', 'r') as f:
            canva_config = json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        canva_config = {}  # Initialize to an empty dictionary if the file is not found or invalid

    return render_template('canva_manage.html', canva_config=canva_config)

@admin_bp.route('/admin/get_canva_config', methods=['GET'])
@login_required
def get_canva_config():
    try:
        with open('configs/services/canva_config.json', 'r') as f:
            canva_config = json.load(f)
        return jsonify(canva_config)
    except (FileNotFoundError, json.JSONDecodeError):
        return jsonify({"error": "Failed to load Canva config"}), 500

@admin_bp.route('/admin/update_canva_config', methods=['POST'])
@login_required
def update_canva_config():
    try:
        new_config = request.json
        # 保持 API_KEY 不变
        with open('configs/services/canva_config.json', 'r') as f:
            current_config = json.load(f)
            api_key = current_config.get('API_KEY', '')

        new_config['API_KEY'] = api_key
        with open('configs/services/canva_config.json', 'w') as f:
            json.dump(new_config, f, indent=4)
        return jsonify({"message": "Canva config updated successfully"}), 200
    except Exception as e:
        return jsonify({"error": f"Failed to update Canva config: {str(e)}"}), 500


@admin_bp.route('/admin/plugins', methods=['GET'])
@login_required
def plugins():
    """Plugin management admin page"""
    return render_template('admin_plugins.html')

@admin_bp.route('/admin/shopee_api_url', methods=['GET'])
@login_required
def get_shopee_api_url():
    """Get current Shopee API URL configuration"""
    try:
        from services.shopee_api_client import shopee_client
        current_url = shopee_client.get_current_base_url()

        # Also get the configured URL from config
        config = load_config()
        config_url = config.get('SHOPEE_API_BASE_URL', '')

        return jsonify({
            "current_url": current_url,
            "config_url": config_url,
            "message": "Shopee API URL retrieved successfully"
        }), 200
    except Exception as e:
        return jsonify({"error": f"Failed to get Shopee API URL: {str(e)}"}), 500

@admin_bp.route('/admin/shopee_api_url', methods=['POST'])
@login_required
def update_shopee_api_url():
    """Update Shopee API URL configuration"""
    try:
        data = request.json
        new_url = data.get('url', '').strip()

        if not new_url:
            return jsonify({"error": "URL is required"}), 400

        # Update the global config
        config = load_config()
        config['SHOPEE_API_BASE_URL'] = new_url
        save_config(config)
        update_config()

        # Update the current client instance
        from services.shopee_api_client import shopee_client
        shopee_client.update_base_url(new_url)

        return jsonify({
            'message': "Shopee API URL updated successfully",
            "new_url": new_url
        }), 200
    except Exception as e:
        return jsonify({"error": f"Failed to update Shopee API URL: {str(e)}"}), 500

@admin_bp.route('/admin/test_shopee_api', methods=['POST'])
@login_required
def test_shopee_api():
    """Test the connection to the Shopee API"""
    try:
        data = request.json
        url = data.get('url', '').strip()

        if not url:
            return jsonify({'success': False, 'error': 'URL is required'}), 400

        # Use the health check endpoint of the Shopee API for testing
        test_url = f"{url}/health"
        
        import requests
        response = requests.get(test_url, timeout=10)

        if response.status_code == 200:
            return jsonify({'success': True, 'message': 'Connection successful'})
        else:
            return jsonify({'success': False, 'error': f'Connection failed with status code: {response.status_code}'}), 500

    except requests.exceptions.RequestException as e:
        return jsonify({'success': False, 'error': f'Connection failed: {str(e)}'}), 500
    except Exception as e:
        return jsonify({'success': False, 'error': f'An unexpected error occurred: {str(e)}'}), 500

@admin_bp.route('/admin/test_email_notification', methods=['POST'])
@login_required
def test_email_notification():
    """Test email notification configuration"""
    import logging
    logger = logging.getLogger(__name__)

    try:
        data = request.json
        email = data.get('email')
        app_password = data.get('app_password')

        if not email or not app_password:
            return jsonify({'success': False, 'error': 'Email and app password are required'}), 400

        # Send test email using simple SMTP
        import smtplib
        from email.mime.text import MIMEText
        from email.mime.multipart import MIMEMultipart

        try:
            # Create message
            message = MIMEMultipart()
            message["From"] = email
            message["To"] = email  # Send to self for testing
            message["Subject"] = "🧪 SteamCodeTool Notification Test"

            body = "This is a test email from SteamCodeTool notification system. If you receive this, your email configuration is working correctly!"
            message.attach(MIMEText(body, "plain", "utf-8"))

            # Connect to Gmail SMTP server and send email
            with smtplib.SMTP("smtp.gmail.com", 587) as server:
                server.starttls()  # Enable TLS encryption
                server.login(email, app_password)
                text = message.as_string()
                server.sendmail(email, email, text)

            success = True
        except Exception as smtp_error:
            logger.error(f"SMTP error: {smtp_error}")
            success = False

        if success:
            return jsonify({
                'success': True,
                'message': 'Test email sent successfully'
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Failed to send test email. Please check your email configuration.'
            }), 500

    except Exception as e:
        logger.error(f"Error testing email notification: {e}")
        return jsonify({
            'success': False,
            'error': f'Email test failed: {str(e)}'
        }), 500

@admin_bp.route('/admin/test_webhook_notification', methods=['POST'])
@login_required
def test_webhook_notification():
    """Test webhook notification configuration"""
    import logging
    import requests
    logger = logging.getLogger(__name__)

    try:
        webhook_config = request.json

        if not webhook_config.get('ENABLED'):
            return jsonify({
                'success': False,
                'error': 'Webhooks are not enabled'
            }), 400

        test_results = []
        overall_success = True

        # Test message received webhook
        if webhook_config.get('MESSAGE_RECEIVED', {}).get('ENABLED'):
            url = webhook_config['MESSAGE_RECEIVED'].get('URL')
            if url:
                try:
                    test_payload = {
                        'event': 'test',
                        'type': 'message_received',
                        'message': {
                            'message_id': 'test-123',
                            'content': {'text': 'Test message from SteamCodeTool'},
                            'from_name': 'Test User',
                            'from_id': 'test-user-id',
                            'send_by_yourself': False,
                            'timestamp': '2024-01-01T12:00:00Z'
                        },
                        'conversation_id': 'test-conversation'
                    }

                    response = requests.post(url, json=test_payload, timeout=10)

                    if response.status_code == 200:
                        test_results.append(f"✅ Message Received webhook: {url} - Success")
                    else:
                        test_results.append(f"❌ Message Received webhook: {url} - Failed (Status: {response.status_code})")
                        overall_success = False

                except Exception as e:
                    test_results.append(f"❌ Message Received webhook: {url} - Error: {str(e)}")
                    overall_success = False
            else:
                test_results.append("⚠️ Message Received webhook: No URL configured")

        # Test message sent webhook
        if webhook_config.get('MESSAGE_SENT', {}).get('ENABLED'):
            url = webhook_config['MESSAGE_SENT'].get('URL')
            if url:
                try:
                    test_payload = {
                        'event': 'test',
                        'type': 'message_sent',
                        'message': {
                            'message_id': 'test-456',
                            'content': {'text': 'Test reply from SteamCodeTool'},
                            'to_name': 'Test Customer',
                            'to_id': 'test-customer-id',
                            'send_by_yourself': True,
                            'timestamp': '2024-01-01T12:01:00Z'
                        },
                        'conversation_id': 'test-conversation'
                    }

                    response = requests.post(url, json=test_payload, timeout=10)

                    if response.status_code == 200:
                        test_results.append(f"✅ Message Sent webhook: {url} - Success")
                    else:
                        test_results.append(f"❌ Message Sent webhook: {url} - Failed (Status: {response.status_code})")
                        overall_success = False

                except Exception as e:
                    test_results.append(f"❌ Message Sent webhook: {url} - Error: {str(e)}")
                    overall_success = False
            else:
                test_results.append("⚠️ Message Sent webhook: No URL configured")

        if not test_results:
            test_results.append("⚠️ No webhook endpoints configured for testing")

        return jsonify({
            'success': overall_success,
            'message': 'Webhook test completed',
            'details': test_results
        })

    except Exception as e:
        logger.error(f"Error testing webhook notification: {e}")
        return jsonify({
            'success': False,
            'error': f'Webhook test failed: {str(e)}'
        }), 500


# Shopee Auto Boost Plugin Admin Routes
@admin_bp.route('/admin/shopee_auto_boost/dashboard')
@login_required
def shopee_auto_boost_dashboard():
    """Shopee Auto Boost Dashboard"""
    return render_template('shopee_auto_boost/dashboard.html')


@admin_bp.route('/admin/shopee_auto_boost/products')
@login_required
def shopee_auto_boost_products():
    """Shopee Auto Boost Products Management"""
    return render_template('shopee_auto_boost/products.html')


@admin_bp.route('/admin/shopee_auto_boost/history')
@login_required
def shopee_auto_boost_history():
    """Shopee Auto Boost History"""
    return render_template('shopee_auto_boost/history.html')


@admin_bp.route('/admin/shopee_auto_boost/settings')
@login_required
def shopee_auto_boost_settings():
    """Shopee Auto Boost Settings"""
    return render_template('shopee_auto_boost/settings.html')


# Dashboard Widget API Endpoints
@admin_bp.route('/admin/api/dashboard/widgets')
@login_required
def get_dashboard_widgets():
    """Get all dashboard widgets from enabled plugins"""
    try:
        plugin_manager = current_app.plugin_manager
        widgets = plugin_manager.get_all_dashboard_widgets()
        
        return jsonify({
            'success': True,
            'widgets': widgets
        })
    except Exception as e:
        logger.error(f"Error fetching dashboard widgets: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@admin_bp.route('/admin/api/widget/<plugin_name>/<widget_id>')
@login_required
def get_widget_data(plugin_name, widget_id):
    """Get data for a specific widget from a plugin"""
    try:
        plugin_manager = current_app.plugin_manager
        data = plugin_manager.get_widget_data(plugin_name, widget_id)
        
        return jsonify({
            'success': True,
            'data': data
        })
    except Exception as e:
        logger.error(f"Error fetching widget data for {plugin_name}.{widget_id}: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500