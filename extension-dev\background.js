let bearerToken = '';
let fullCookieText = '';
let cookieJsonArray = [];
let isTokenCaptured = false;
let isCookieCaptured = false;

chrome.webRequest.onBeforeSendHeaders.addListener(
  (details) => {
    if (isTokenCaptured) return;

    for (let header of details.requestHeaders) {
      if (header.name.toLowerCase() === 'authorization' && header.value.startsWith('Bearer ')) {
        bearerToken = header.value;
        chrome.storage.local.set({ bearerToken });
        isTokenCaptured = true;
        chrome.webRequest.onBeforeSendHeaders.removeListener(arguments.callee);
        console.log('Bearer token captured, listener removed.');
        break;
      }
    }
  },
  { urls: ["*://seller.shopee.com.my/*"] },
  ["requestHeaders", "extraHeaders"]
);

function updateCookies(domain) {
  if (isCookieCaptured) return;

  // Get cookies from multiple sources to ensure we capture everything
  const cookieSources = [
    { domain: domain },
    { url: `https://${domain}` },
    { url: `https://seller.shopee.com.my` },
    { url: `https://shopee.com.my` }
  ];

  let allCookies = [];
  let completedRequests = 0;

  cookieSources.forEach(source => {
    chrome.cookies.getAll(source, (cookies) => {
      // Add cookies to the collection, avoiding duplicates
      cookies.forEach(cookie => {
        const exists = allCookies.some(existing =>
          existing.name === cookie.name &&
          existing.domain === cookie.domain &&
          existing.path === cookie.path
        );
        if (!exists) {
          allCookies.push(cookie);
        }
      });

      completedRequests++;

      // Process when all requests are complete
      if (completedRequests === cookieSources.length) {
        processCookies(allCookies);
      }
    });
  });
}

function processCookies(cookies) {
  console.log(`Processing ${cookies.length} total cookies`);

  // Check if we have the important SPC_CDS_CHAT cookie
  const spcCdsChatCookie = cookies.find(cookie => cookie.name === "SPC_CDS_CHAT");
  if (spcCdsChatCookie) {
    console.log("✅ SPC_CDS_CHAT cookie found:", spcCdsChatCookie.value.substring(0, 20) + "...");
  } else {
    console.log("❌ SPC_CDS_CHAT cookie NOT found");
  }

  // Store as string format for backward compatibility
  fullCookieText = cookies.map(cookie => `${cookie.name}=${cookie.value}`).join('; ');

  // Store as JSON array format with all available properties
  cookieJsonArray = cookies.map(cookie => {
    // Create a complete cookie object with all properties
    const cookieObj = {
      name: cookie.name,
      value: cookie.value,
      domain: cookie.domain,
      path: cookie.path,
      secure: cookie.secure,
      httpOnly: cookie.httpOnly,
      sameSite: cookie.sameSite,
      hostOnly: cookie.hostOnly
    };

    // Add expiration date if available
    if (cookie.expirationDate) {
      cookieObj.expirationDate = cookie.expirationDate;
    }

    // Add any other properties that might be available
    if (cookie.storeId) cookieObj.storeId = cookie.storeId;
    if (cookie.session) cookieObj.session = cookie.session;

    return cookieObj;
  });

  // Save both formats to storage
  chrome.storage.local.set({
    fullCookieText,
    cookieJsonArray: JSON.stringify(cookieJsonArray)
  });

  console.log("Cookies saved to storage. String length:", fullCookieText.length);
  console.log("JSON array length:", cookieJsonArray.length);

  isCookieCaptured = true;
  checkAndRemoveListeners();
}

// Initialize cookie capture from primary domain
updateCookies("seller.shopee.com.my");


const cookieListener = (changeInfo) => {
  if (isCookieCaptured) return;

  const domain = changeInfo.cookie.domain;
  if (domain.includes("seller.shopee.com.my") || domain.includes("shopee.com.my")) {
    updateCookies(domain);
  }
};

chrome.cookies.onChanged.addListener(cookieListener);

function checkAndRemoveListeners() {
  if (isTokenCaptured && isCookieCaptured) {
    chrome.webRequest.onBeforeSendHeaders.removeListener(arguments.callee);
    chrome.cookies.onChanged.removeListener(cookieListener);
    console.log('All data captured, listeners removed.');
  }
}

// Handle messages from popup
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'refreshCookies') {
    console.log('Received refresh cookies request');

    // Reset the capture flag to allow recapture
    isCookieCaptured = false;

    // Clear existing cookie data
    fullCookieText = '';
    cookieJsonArray = [];

    // Recapture cookies
    updateCookies("seller.shopee.com.my");

    // Send response
    sendResponse({ success: true });

    return true; // Keep the message channel open for async response
  }
});