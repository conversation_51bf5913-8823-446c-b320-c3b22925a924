"""
Product Service for Shopee Auto Boost Plugin

Handles fetching and filtering products for boosting via local ShopeeAPI service.
"""

import logging
import requests
import json
import os
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional

logger = logging.getLogger(__name__)


class ProductService:
    """Service for managing product data and filtering via local ShopeeAPI"""

    def __init__(self, shopee_plugin, config: Dict[str, Any]):
        # shopee_plugin is now unused (kept for compatibility)
        self.shopee_plugin = shopee_plugin
        self.config = config
        self.api_base_url = config.get("shopee_api_url", "http://localhost:8000")
        self.session = requests.Session()
        # Set timeout for API calls
        self.timeout = 30
        
        # Cache configuration
        self.cache_duration_hours = config.get("cache_duration_hours", 2)  # Default 2 hours cache
        self.cache_dir = "configs/cache/shopee_auto_boost"
        self.products_cache_file = os.path.join(self.cache_dir, "products_cache.json")
        self.pinned_products_file = os.path.join("configs/data", "shopee_auto_boost_pinned.json")

        # Ensure directories exist
        os.makedirs(self.cache_dir, exist_ok=True)
        os.makedirs(os.path.dirname(self.pinned_products_file), exist_ok=True)

        # Load pinned products
        self._load_pinned_products()

    def _is_cache_valid(self) -> bool:
        """Check if the cached products are still valid"""
        try:
            if not os.path.exists(self.products_cache_file):
                return False
            
            with open(self.products_cache_file, 'r', encoding='utf-8') as f:
                cache_data = json.load(f)
            
            cache_time = datetime.fromisoformat(cache_data.get('timestamp', ''))
            expiry_time = cache_time + timedelta(hours=self.cache_duration_hours)
            
            return datetime.now() < expiry_time
        except Exception as e:
            logger.warning(f"Error checking cache validity: {e}")
            return False

    def _load_cached_products(self) -> List[Dict[str, Any]]:
        """Load products from cache"""
        try:
            with open(self.products_cache_file, 'r', encoding='utf-8') as f:
                cache_data = json.load(f)
            
            products = cache_data.get('products', [])
            logger.info(f"Loaded {len(products)} products from cache")
            return products
        except Exception as e:
            logger.error(f"Error loading cached products: {e}")
            return []

    def _save_products_to_cache(self, products: List[Dict[str, Any]]) -> None:
        """Save products to cache"""
        try:
            cache_data = {
                'timestamp': datetime.now().isoformat(),
                'products': products,
                'total_count': len(products)
            }
            
            with open(self.products_cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"Cached {len(products)} products")
        except Exception as e:
            logger.error(f"Error saving products to cache: {e}")

    def _load_pinned_products(self) -> None:
        """Load pinned products from JSON file"""
        try:
            if os.path.exists(self.pinned_products_file):
                with open(self.pinned_products_file, 'r', encoding='utf-8') as f:
                    pinned_data = json.load(f)
                    self.config["pinned_products"] = pinned_data
                    logger.info(f"Loaded {len(pinned_data.get('product_ids', []))} pinned products from file")
            else:
                self.config["pinned_products"] = {"product_ids": []}
        except Exception as e:
            logger.error(f"Error loading pinned products: {e}")
            self.config["pinned_products"] = {"product_ids": []}

    def _save_pinned_products(self) -> None:
        """Save pinned products to JSON file"""
        try:
            with open(self.pinned_products_file, 'w', encoding='utf-8') as f:
                json.dump(self.config.get("pinned_products", {}), f, ensure_ascii=False, indent=2)
            logger.info("Pinned products saved to file")
        except Exception as e:
            logger.error(f"Error saving pinned products: {e}")

    def get_cache_info(self) -> Dict[str, Any]:
        """Get information about the current cache"""
        try:
            if not os.path.exists(self.products_cache_file):
                return {
                    "cached": False,
                    "cache_age_hours": 0,
                    "total_products": 0,
                    "cache_valid": False
                }
            
            with open(self.products_cache_file, 'r', encoding='utf-8') as f:
                cache_data = json.load(f)
            
            cache_time = datetime.fromisoformat(cache_data.get('timestamp', ''))
            age_hours = (datetime.now() - cache_time).total_seconds() / 3600
            
            return {
                "cached": True,
                "cache_timestamp": cache_data.get('timestamp'),
                "cache_age_hours": round(age_hours, 2),
                "total_products": cache_data.get('total_count', 0),
                "cache_valid": self._is_cache_valid(),
                "cache_duration_hours": self.cache_duration_hours
            }
        except Exception as e:
            logger.error(f"Error getting cache info: {e}")
            return {"error": str(e)}

    def clear_cache(self) -> bool:
        """Clear the products cache"""
        try:
            if os.path.exists(self.products_cache_file):
                os.remove(self.products_cache_file)
                logger.info("Products cache cleared")
                return True
            return True
        except Exception as e:
            logger.error(f"Error clearing cache: {e}")
            return False

    def get_boostable_products(self, use_cache: bool = True) -> List[Dict[str, Any]]:
        """Get all products that can be boosted via local ShopeeAPI"""
        try:
            # Check if we should use cache and if cache is valid
            if use_cache and self._is_cache_valid():
                logger.info("Using cached products data")
                return self._load_cached_products()

            # Fetch fresh data from API
            logger.info("Fetching fresh products data from API")
            filters = self.config.get("product_filters", {})

            url = f"{self.api_base_url}/products/boostable"
            params = {
                "min_stock": filters.get("min_stock", 1),
                "exclude_inactive": filters.get("exclude_inactive", True),
                "exclude_unlisted": filters.get("exclude_unlisted", True)
            }

            response = self.session.get(url, params=params, timeout=self.timeout)
            response.raise_for_status()

            data = response.json()
            products = data.get("data", [])

            # Save to cache for future use
            if use_cache:
                self._save_products_to_cache(products)

            logger.info(f"Found {len(products)} boostable products from local API")
            return products

        except Exception as e:
            logger.error(f"Error getting boostable products from local API: {e}")
            # If API fails and we have cache, try to use it as fallback
            if use_cache and os.path.exists(self.products_cache_file):
                logger.info("API failed, falling back to cached data")
                return self._load_cached_products()
            
            # If no cache available, return mock data for testing
            logger.info("No cache available, returning mock data for testing")
            return self._get_mock_products()

    def _get_mock_products(self) -> List[Dict[str, Any]]:
        """Get mock products for testing when API is not available"""
        mock_products = [
            {
                "id": 12345,
                "name": "Sample Product 1 - VPN Config",
                "stock": 50,
                "sold_count": 120,
                "price_min": "10.00",
                "price_max": "15.00",
                "boost_status": 1,
                "status": "active"
            },
            {
                "id": 12346,
                "name": "Sample Product 2 - Steam Code",
                "stock": 25,
                "sold_count": 85,
                "price_min": "5.00",
                "price_max": "8.00",
                "boost_status": 1,
                "status": "active"
            },
            {
                "id": 12347,
                "name": "Sample Product 3 - Netflix Account",
                "stock": 10,
                "sold_count": 200,
                "price_min": "20.00",
                "price_max": "25.00",
                "boost_status": 2,
                "status": "active"
            },
            {
                "id": 12348,
                "name": "Sample Product 4 - Canva Pro",
                "stock": 75,
                "sold_count": 45,
                "price_min": "12.00",
                "price_max": "18.00",
                "boost_status": 1,
                "status": "active"
            },
            {
                "id": 12349,
                "name": "Sample Product 5 - Digital Service",
                "stock": 5,
                "sold_count": 300,
                "price_min": "30.00",
                "price_max": "35.00",
                "boost_status": 3,
                "status": "active"
            }
        ]
        
        logger.info(f"Generated {len(mock_products)} mock products for testing")
        return mock_products

    def get_all_products(self) -> List[Dict[str, Any]]:
        """Get all products via local ShopeeAPI"""
        try:
            url = f"{self.api_base_url}/products"

            response = self.session.get(url, timeout=self.timeout)
            response.raise_for_status()

            data = response.json()
            products = data.get("data", [])

            logger.info(f"Retrieved {len(products)} products from local API")
            return products

        except Exception as e:
            logger.error(f"Error getting all products from local API: {e}")
            return []

    def get_product_summary(self, product: Dict[str, Any]) -> Dict[str, Any]:
        """Get a summary of product information (products from local API are already summarized)"""
        # Products from local API are already in summary format
        return product

    def get_pinned_products(self) -> List[int]:
        """Get list of pinned product IDs"""
        pinned_config = self.config.get("pinned_products", {})
        return pinned_config.get("product_ids", [])

    def is_product_pinned(self, product_id: int) -> bool:
        """Check if a product is pinned"""
        pinned_products = self.get_pinned_products()
        return product_id in pinned_products

    def pin_product(self, product_id: int) -> bool:
        """Add a product to the pinned list"""
        try:
            pinned_config = self.config.setdefault("pinned_products", {})
            pinned_products = pinned_config.setdefault("product_ids", [])

            if product_id not in pinned_products:
                pinned_products.append(product_id)
                self._save_pinned_products()
                logger.info(f"Product {product_id} added to pinned list")
                return True
            else:
                logger.warning(f"Product {product_id} is already pinned")
                return False

        except Exception as e:
            logger.error(f"Error pinning product {product_id}: {e}")
            return False

    def unpin_product(self, product_id: int) -> bool:
        """Remove a product from the pinned list"""
        try:
            pinned_config = self.config.get("pinned_products", {})
            pinned_products = pinned_config.get("product_ids", [])

            if product_id in pinned_products:
                pinned_products.remove(product_id)
                self._save_pinned_products()
                logger.info(f"Product {product_id} removed from pinned list")
                return True
            else:
                logger.warning(f"Product {product_id} is not pinned")
                return False

        except Exception as e:
            logger.error(f"Error unpinning product {product_id}: {e}")
            return False

    def get_bumped_products(self) -> Dict[str, Any]:
        """Get list of bumped products with cooldown information via local ShopeeAPI"""
        try:
            url = f"{self.api_base_url}/products/bumped"
            
            response = self.session.get(url, timeout=self.timeout)
            response.raise_for_status()
            
            data = response.json()
            
            if data.get("code") == 0:
                bumped_data = data.get("data", {})
                config = bumped_data.get("config", {})
                products = bumped_data.get("products", [])
                
                # Calculate remaining cooldown time for each product
                for product in products:
                    cooldown_seconds = product.get("cool_down_seconds", 0)
                    if cooldown_seconds > 0:
                        hours = cooldown_seconds // 3600
                        minutes = (cooldown_seconds % 3600) // 60
                        product["cooldown_display"] = f"{hours}h {minutes}m"
                        product["can_boost_at"] = datetime.now() + timedelta(seconds=cooldown_seconds)
                    else:
                        product["cooldown_display"] = "Ready"
                        product["can_boost_at"] = datetime.now()
                
                return {
                    "success": True,
                    "config": config,
                    "products": products,
                    "total_slots": config.get("total_slots", 5),
                    "cooldown_seconds": config.get("cooldown_seconds", 14400),
                    "available_slots": config.get("total_slots", 5) - len(products)
                }
            else:
                logger.error(f"API returned error: {data.get('message', 'Unknown error')}")
                return {
                    "success": False,
                    "error": data.get("message", "Unknown error"),
                    "products": [],
                    "total_slots": 5,
                    "available_slots": 5
                }
                
        except Exception as e:
            logger.error(f"Error getting bumped products from local API: {e}")
            return {
                "success": False,
                "error": str(e),
                "products": [],
                "total_slots": 5,
                "available_slots": 5
            }

    def get_pinned_products_info(self) -> Dict[str, Any]:
        """Get detailed information about pinned products"""
        try:
            pinned_config = self.config.get("pinned_products", {})
            pinned_product_ids = pinned_config.get("product_ids", [])

            if not pinned_product_ids:
                return {
                    "pinned_products": [],
                    "total_pinned": 0,
                    "enabled": pinned_config.get("enabled", False),
                    "cooldown_hours": pinned_config.get("cooldown_hours", 4)
                }

            # Get all products to find pinned ones
            all_products = self.get_boostable_products()
            pinned_products_info = []

            for product in all_products:
                if product.get("id") in pinned_product_ids:
                    pinned_products_info.append({
                        "id": product.get("id"),
                        "name": product.get("name", "Unknown"),
                        "status": product.get("status"),
                        "stock": product.get("stock_detail", {}).get("total_available_stock", 0),
                        "boost_eligible": product.get("boost_info", {}).get("boost_entry_status") == 1
                    })

            # Add any pinned products that weren't found in boostable products
            found_ids = {p["id"] for p in pinned_products_info}
            for product_id in pinned_product_ids:
                if product_id not in found_ids:
                    pinned_products_info.append({
                        "id": product_id,
                        "name": "Unknown (Not Found)",
                        "status": "unknown",
                        "stock": 0,
                        "boost_eligible": False
                    })

            return {
                "pinned_products": pinned_products_info,
                "total_pinned": len(pinned_product_ids),
                "enabled": pinned_config.get("enabled", False),
                "cooldown_hours": pinned_config.get("cooldown_hours", 4)
            }

        except Exception as e:
            logger.error(f"Error getting pinned products info: {e}")
            return {"error": str(e)}